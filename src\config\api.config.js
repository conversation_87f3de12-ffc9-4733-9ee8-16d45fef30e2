/**
 * 前端 API 配置
 * 管理前端与后端的连接配置
 */

// 获取环境变量或使用默认值
const getEnvVar = (name, defaultValue) => {
  return import.meta.env?.[name] || defaultValue;
};

// 检测当前环境
const isDevelopment = import.meta.env.MODE === 'development';
const isProduction = import.meta.env.MODE === 'production';

// 默认配置
const DEFAULT_CONFIG = {
  development: {
    API_BASE_URL: 'http://localhost:3000',
    WS_URL: 'ws://localhost:3000/ws'
  },
  production: {
    API_BASE_URL: 'http://localhost:3000',
    WS_URL: 'ws://localhost:3000/ws'
  }
};

// 获取当前配置
export const getApiConfig = () => {
  const env = isDevelopment ? 'development' : 'production';
  
  return {
    API_BASE_URL: getEnvVar('VITE_API_BASE_URL', DEFAULT_CONFIG[env].API_BASE_URL),
    WS_URL: getEnvVar('VITE_WS_URL', DEFAULT_CONFIG[env].WS_URL),
    isDevelopment,
    isProduction
  };
};

// 导出配置
export const API_CONFIG = getApiConfig();

// 默认导出
export default API_CONFIG;
