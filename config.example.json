{"server": {"host": "0.0.0.0", "port": 5000, "description": "后端服务器配置 - host设置为0.0.0.0允许外部访问，port为服务端口"}, "frontend": {"host": "0.0.0.0", "port": 5000, "description": "前端访问配置，通常与后端端口相同"}, "api": {"baseUrl": "http://YOUR_SERVER_IP:5000", "description": "API基础URL，部署时需要修改YOUR_SERVER_IP为实际服务器IP地址"}, "websocket": {"url": "ws://YOUR_SERVER_IP:5000", "description": "WebSocket连接URL，部署时需要修改YOUR_SERVER_IP为实际服务器IP地址"}, "deployment": {"examples": {"localhost": {"api": {"baseUrl": "http://localhost:5000"}, "websocket": {"url": "ws://localhost:5000"}}, "lan": {"api": {"baseUrl": "http://*************:5000"}, "websocket": {"url": "ws://*************:5000"}}, "public": {"api": {"baseUrl": "http://your-domain.com:5000"}, "websocket": {"url": "ws://your-domain.com:5000"}}}}}