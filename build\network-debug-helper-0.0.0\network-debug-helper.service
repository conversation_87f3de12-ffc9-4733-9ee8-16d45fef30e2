[Unit]
Description=Network Debug Helper
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/opt/network-debug-helper
ExecStart=/usr/bin/node server/server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=HOST=0.0.0.0

# 日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=network-debug-helper

[Install]
WantedBy=multi-user.target
