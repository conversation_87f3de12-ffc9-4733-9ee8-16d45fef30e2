#!/bin/bash

# PM2 部署脚本 - 适用于 CentOS 生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 PM2 是否安装
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_info "PM2 未安装，正在安装..."
        npm install -g pm2
        log_success "PM2 安装完成"
    else
        log_info "PM2 已安装: $(pm2 --version)"
    fi
}

# 创建日志目录
create_log_directory() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_info "创建日志目录: logs/"
    fi
}

# 停止现有进程
stop_existing_processes() {
    log_info "停止现有进程..."
    pm2 stop network-debug-helper-backend 2>/dev/null || true
    pm2 stop network-debug-helper-frontend 2>/dev/null || true
    pm2 delete network-debug-helper-backend 2>/dev/null || true
    pm2 delete network-debug-helper-frontend 2>/dev/null || true
}

# 构建项目
build_project() {
    log_info "构建项目..."
    npm install
    npm run build
    log_success "项目构建完成"
}

# 启动 PM2 服务
start_pm2_services() {
    log_info "启动 PM2 服务..."
    pm2 start ecosystem.config.js --env production
    
    # 保存 PM2 进程列表
    pm2 save
    
    # 设置 PM2 开机自启
    pm2 startup
    
    log_success "PM2 服务启动完成"
}

# 显示服务状态
show_status() {
    echo
    log_info "服务状态:"
    pm2 list
    
    echo
    log_info "服务详情:"
    pm2 show network-debug-helper-backend
    pm2 show network-debug-helper-frontend
}

# 显示部署信息
show_deployment_info() {
    SERVER_IP=$(hostname -I | awk '{print $1}')
    
    echo
    log_success "🎉 PM2 部署完成！"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${GREEN}📱 访问地址:${NC}"
    echo -e "   前端: http://$SERVER_IP:5000"
    echo -e "   后端: http://$SERVER_IP:3000"
    echo
    echo -e "${GREEN}🛠️  PM2 管理命令:${NC}"
    echo -e "   查看状态: pm2 list"
    echo -e "   查看日志: pm2 logs"
    echo -e "   重启服务: pm2 restart all"
    echo -e "   停止服务: pm2 stop all"
    echo -e "   查看监控: pm2 monit"
    echo -e "   重载配置: pm2 reload ecosystem.config.js"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 主函数
main() {
    echo "🚀 PM2 部署脚本 - 网络调试助手"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    check_pm2
    create_log_directory
    stop_existing_processes
    build_project
    start_pm2_services
    show_status
    show_deployment_info
}

main "$@"
