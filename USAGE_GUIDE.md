# 网络调试助手使用指南

## 发送和接收格式设置

### 🔧 发送设置

发送设置控制您要发送的数据格式：

#### ASCII 模式
- **用途**: 发送普通文本字符串
- **示例**: 
  - 输入: `Hello World`
  - 实际发送: `Hello World` (UTF-8 编码)

#### HEX 模式
- **用途**: 发送十六进制格式的二进制数据
- **格式要求**: 
  - 只能包含 0-9 和 A-F 字符
  - 字符数必须是偶数
  - 可以包含空格分隔
- **示例**:
  - 输入: `48 5A 00 00 00 00 00 01 01 00 00 00 00 02 CC C1`
  - 实际发送: 对应的 16 字节二进制数据

### 📥 接收设置

接收设置控制如何显示接收到的数据：

#### ASCII 模式显示
- **显示方式**: 将接收到的二进制数据解释为 ASCII 字符
- **不可打印字符**: 显示为点号 `.`
- **示例**: 
  - 接收数据: `48 5A 00 00 00 00 00 01 01 00 00 00 00 02 CC C1`
  - ASCII 显示: `HZ..........`

#### HEX 模式显示
- **显示方式**: 将接收到的数据显示为十六进制格式
- **格式**: 每个字节用两位十六进制表示，用空格分隔
- **示例**:
  - 接收数据: 任何二进制数据
  - HEX 显示: `48 5A 00 00 00 00 00 01 01 00 00 00 00 02 CC C1`

## 🚀 使用场景

### 场景 1: 调试文本协议
- **发送设置**: ASCII
- **接收设置**: ASCII
- **适用**: HTTP、SMTP、FTP 等文本协议

### 场景 2: 调试二进制协议
- **发送设置**: HEX
- **接收设置**: HEX
- **适用**: 自定义二进制协议、设备通信

### 场景 3: 混合调试
- **发送设置**: ASCII（发送命令）
- **接收设置**: HEX（查看原始响应）
- **适用**: 需要发送文本命令但查看二进制响应的场景

## 💡 使用技巧

### HEX 格式输入技巧
1. **空格分隔**: `48 5A CC C1` ✅
2. **无空格**: `485ACCC1` ✅
3. **混合大小写**: `48 5a CC c1` ✅
4. **错误格式**: `48 5Z CC C1` ❌ (包含非十六进制字符)
5. **奇数字符**: `48 5A C` ❌ (字符数必须是偶数)

### 数据长度计算
- **ASCII 模式**: 字符数 = 字节数
- **HEX 模式**: 十六进制字符对数 = 字节数
  - 例如: `48 5A CC C1` = 4 字节

### 常用十六进制值
- **换行符**: `0A` (LF) 或 `0D 0A` (CRLF)
- **空字符**: `00`
- **空格**: `20`
- **数字 0-9**: `30-39`
- **字母 A-Z**: `41-5A`
- **字母 a-z**: `61-7A`

## 🔍 故障排除

### 发送失败
1. **检查连接状态**: 确保 TCP 连接已建立
2. **验证 HEX 格式**: 确保十六进制格式正确
3. **检查网络**: 确保目标主机可达

### 显示异常
1. **乱码问题**: 尝试切换到 HEX 模式查看原始数据
2. **数据丢失**: 检查网络连接稳定性
3. **格式错误**: 确认发送和接收格式设置正确

## 📋 快速测试

### 本地回环测试
1. 启动 TCP Server 模式，监听 `127.0.0.1:8080`
2. 使用另一个客户端连接到 `127.0.0.1:8080`
3. 测试不同格式的数据发送和接收

### 设备通信测试
1. 连接到目标设备
2. 根据设备协议选择合适的发送格式
3. 根据调试需要选择合适的接收显示格式
