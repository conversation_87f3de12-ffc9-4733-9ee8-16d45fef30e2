#!/bin/bash

# CentOS 安装脚本
set -e

echo "🚀 开始在 CentOS 上安装网络调试助手..."

# 检查 Node.js
echo "📋 检查 Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
    echo "安装命令："
    echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
    echo "sudo yum install -y nodejs"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，需要 16+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 安装依赖
echo "📦 安装依赖..."
npm install --production

echo "✅ 安装完成！"
echo ""
echo "🚀 启动应用："
echo "npm start"
echo ""
echo "🔍 检查状态："
echo "npm run check"
echo ""
echo "🌐 访问地址："
echo "http://localhost:5000"
