@echo off
chcp 65001 >nul

echo 🚀 开始部署网络调试助手...

REM 检查 Node.js 版本
echo 📋 检查环境...
node -v
if %errorlevel% neq 0 (
    echo ❌ 请先安装 Node.js
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 安装依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 构建前端
echo 🔨 构建前端...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

REM 检查构建结果
if exist "dist" (
    echo ✅ 前端构建成功
) else (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

REM 启动服务
echo 🌟 启动服务...
echo 前端将在 http://localhost:5000 运行
echo 后端 API 将在 http://localhost:3000 运行
echo.
echo 按 Ctrl+C 停止服务

REM 设置生产环境并启动
set NODE_ENV=production
npm run start
