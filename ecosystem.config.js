module.exports = {
  apps: [
    {
      name: 'network-debug-helper',
      script: 'server/server.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        FRONTEND_PORT: 5000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        FRONTEND_PORT: 5000
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true
    },
    {
      name: 'network-debug-helper-frontend',
      script: 'server/static-server.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        STATIC_DIR: './public'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        STATIC_DIR: './public'
      },
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: './logs/frontend-err.log',
      out_file: './logs/frontend-out.log',
      log_file: './logs/frontend-combined.log',
      time: true
    }
  ]
};
