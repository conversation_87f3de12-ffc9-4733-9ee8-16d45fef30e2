#!/usr/bin/env node

/**
 * CentOS 打包脚本
 * 创建可在 CentOS 上部署的完整包
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import archiver from 'archiver';

// 颜色输出
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 获取包信息
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const version = packageJson.version;
const packageName = 'network-debug-helper';

// 配置
const buildDir = 'build';
const packageDir = path.join(buildDir, `${packageName}-${version}`);
const archiveName = `${packageName}-${version}-centos.tar.gz`;

async function createPackage() {
    try {
        log('🚀 开始打包网络调试助手 for CentOS...', 'blue');

        // 清理之前的构建
        log('🧹 清理之前的构建...', 'yellow');
        if (fs.existsSync(buildDir)) {
            fs.rmSync(buildDir, { recursive: true, force: true });
        }
        fs.mkdirSync(packageDir, { recursive: true });

        // 构建前端
        log('🔨 构建前端...', 'yellow');
        execSync('npm run build', { stdio: 'inherit' });

        // 复制文件
        log('📋 复制文件...', 'yellow');
        
        // 复制构建后的前端文件
        copyDirectory('dist', path.join(packageDir, 'dist'));
        
        // 复制后端源码
        copyDirectory('server', path.join(packageDir, 'server'));
        
        // 复制必要文件
        const filesToCopy = [
            'README.md',
            'CENTOS_DEPLOY_GUIDE.md',
            'check-deployment.js'
        ];
        
        filesToCopy.forEach(file => {
            if (fs.existsSync(file)) {
                fs.copyFileSync(file, path.join(packageDir, file));
            }
        });

        // 创建生产环境的 package.json
        log('📝 创建生产环境 package.json...', 'yellow');
        const prodPackageJson = {
            name: packageJson.name,
            version: packageJson.version,
            description: packageJson.description,
            type: packageJson.type,
            scripts: {
                'start': 'node server/server.js',
                'check': 'node check-deployment.js'
            },
            dependencies: {
                'express': packageJson.dependencies.express,
                'ws': packageJson.dependencies.ws,
                'cors': packageJson.dependencies.cors
            }
        };
        
        fs.writeFileSync(
            path.join(packageDir, 'package.json'),
            JSON.stringify(prodPackageJson, null, 2)
        );

        // 创建安装脚本
        log('📜 创建安装脚本...', 'yellow');
        const installScript = `#!/bin/bash

# CentOS 安装脚本
set -e

echo "🚀 开始在 CentOS 上安装网络调试助手..."

# 检查 Node.js
echo "📋 检查 Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
    echo "安装命令："
    echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
    echo "sudo yum install -y nodejs"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，需要 16+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 安装依赖
echo "📦 安装依赖..."
npm install --production

echo "✅ 安装完成！"
echo ""
echo "🚀 启动应用："
echo "npm start"
echo ""
echo "🔍 检查状态："
echo "npm run check"
echo ""
echo "🌐 访问地址："
echo "http://localhost:5000"
`;

        fs.writeFileSync(path.join(packageDir, 'install.sh'), installScript);

        // 创建 systemd 服务文件
        const serviceFile = `[Unit]
Description=Network Debug Helper
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/opt/network-debug-helper
ExecStart=/usr/bin/node server/server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=HOST=0.0.0.0

# 日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=network-debug-helper

[Install]
WantedBy=multi-user.target
`;

        fs.writeFileSync(path.join(packageDir, 'network-debug-helper.service'), serviceFile);

        // 创建部署说明
        const deployGuide = `# CentOS 部署指南

## 快速部署

### 1. 解压文件
\`\`\`bash
tar -xzf ${archiveName}
cd ${packageName}-${version}
\`\`\`

### 2. 运行安装脚本
\`\`\`bash
chmod +x install.sh
./install.sh
\`\`\`

### 3. 启动应用
\`\`\`bash
npm start
\`\`\`

### 4. 验证部署
\`\`\`bash
npm run check
\`\`\`

## 访问地址

- 前端界面: http://localhost:5000
- 后端 API: http://localhost:3000

## 生产环境部署

### 使用 systemd 服务

1. 复制到系统目录：
\`\`\`bash
sudo cp -r . /opt/network-debug-helper
sudo chown -R nodejs:nodejs /opt/network-debug-helper
\`\`\`

2. 安装服务：
\`\`\`bash
sudo cp network-debug-helper.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable network-debug-helper
sudo systemctl start network-debug-helper
\`\`\`

3. 检查状态：
\`\`\`bash
sudo systemctl status network-debug-helper
\`\`\`
`;

        fs.writeFileSync(path.join(packageDir, 'CENTOS_DEPLOY.md'), deployGuide);

        // 创建压缩包
        log('📦 创建压缩包...', 'yellow');
        await createArchive(packageDir, archiveName);

        // 完成
        const stats = fs.statSync(archiveName);
        log('✅ 打包完成！', 'green');
        log('');
        log(`📦 打包文件: ${archiveName}`, 'blue');
        log(`📁 大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`, 'blue');
        log('');
        log('🚀 CentOS 部署步骤：', 'green');
        log(`1. 上传 ${archiveName} 到 CentOS 服务器`);
        log(`2. tar -xzf ${archiveName}`);
        log(`3. cd ${packageName}-${version}`);
        log('4. chmod +x install.sh && ./install.sh');
        log('5. npm start');
        log('');
        log('🌐 访问地址: http://服务器IP:5000', 'green');

    } catch (error) {
        log(`❌ 打包失败: ${error.message}`, 'red');
        process.exit(1);
    }
}

function copyDirectory(src, dest) {
    if (!fs.existsSync(src)) return;
    
    fs.mkdirSync(dest, { recursive: true });
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        
        if (entry.isDirectory()) {
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

function createArchive(sourceDir, outputPath) {
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(outputPath);
        const archive = archiver('tar', {
            gzip: true,
            gzipOptions: {
                level: 9
            }
        });

        output.on('close', () => {
            resolve();
        });

        archive.on('error', (err) => {
            reject(err);
        });

        archive.pipe(output);
        archive.directory(sourceDir, path.basename(sourceDir));
        archive.finalize();
    });
}

// 运行打包
createPackage();
