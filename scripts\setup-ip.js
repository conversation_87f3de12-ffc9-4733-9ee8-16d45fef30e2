#!/usr/bin/env node

/**
 * IP 配置设置脚本
 * 快速配置网络调试助手的IP地址
 */

import fs from 'fs';
import path from 'path';
import readline from 'readline';

// 颜色输出
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 创建readline接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 提问函数
function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

// 获取本机IP地址
function getLocalIPs() {
    const { networkInterfaces } = await import('os');
    const nets = networkInterfaces();
    const ips = [];

    for (const name of Object.keys(nets)) {
        for (const net of nets[name]) {
            if (net.family === 'IPv4' && !net.internal) {
                ips.push({ name, address: net.address });
            }
        }
    }
    return ips;
}

// 验证IP地址格式
function isValidIP(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip) || ip === 'localhost' || ip === '0.0.0.0';
}

// 验证端口号
function isValidPort(port) {
    const portNum = parseInt(port);
    return !isNaN(portNum) && portNum > 0 && portNum <= 65535;
}

// 创建.env文件
function createEnvFile(config) {
    const envContent = `# 网络调试助手配置
# 由 setup-ip.js 脚本生成于 ${new Date().toLocaleString()}

# ================================
# 后端服务器配置
# ================================
SERVER_HOST=${config.serverHost}
SERVER_PORT=${config.serverPort}

# 生产环境后端配置
PROD_SERVER_HOST=${config.serverHost}
PROD_SERVER_PORT=${config.serverPort}

# ================================
# 前端配置
# ================================
CLIENT_DEV_PORT=5174
CLIENT_PROD_PORT=${config.frontendPort}
CLIENT_HOST=true

# ================================
# API 连接配置
# ================================
API_BASE_URL=http://${config.apiHost}:${config.serverPort}
WS_URL=ws://${config.apiHost}:${config.serverPort}/ws

# 生产环境 API 配置
PROD_API_BASE_URL=http://${config.apiHost}:${config.serverPort}
PROD_WS_URL=ws://${config.apiHost}:${config.serverPort}/ws

# ================================
# 运行环境
# ================================
NODE_ENV=${config.environment}
`;

    fs.writeFileSync('.env', envContent);
    log('✅ .env 文件已创建', 'green');
}

// 显示配置预览
function showConfigPreview(config) {
    log('\n📋 配置预览:', 'blue');
    log('─'.repeat(50), 'cyan');
    log(`后端监听地址: ${config.serverHost}:${config.serverPort}`, 'cyan');
    log(`前端访问地址: http://${config.frontendHost}:${config.frontendPort}`, 'cyan');
    log(`API 连接地址: http://${config.apiHost}:${config.serverPort}`, 'cyan');
    log(`WebSocket 地址: ws://${config.apiHost}:${config.serverPort}/ws`, 'cyan');
    log(`运行环境: ${config.environment}`, 'cyan');
    log('─'.repeat(50), 'cyan');
}

// 主配置流程
async function setupConfiguration() {
    log('🚀 网络调试助手 IP 配置向导', 'blue');
    log('');

    try {
        // 获取本机IP
        const localIPs = await getLocalIPs();
        
        log('🔍 检测到的网络接口:', 'yellow');
        localIPs.forEach((ip, index) => {
            log(`  ${index + 1}. ${ip.name}: ${ip.address}`, 'cyan');
        });
        log('');

        // 选择配置模式
        log('📋 请选择配置模式:', 'yellow');
        log('  1. 本地开发 (localhost)');
        log('  2. 局域网访问 (自动检测IP)');
        log('  3. 自定义配置');
        log('');

        const mode = await question('请选择模式 (1-3): ');
        let config = {};

        switch (mode) {
            case '1':
                // 本地开发模式
                config = {
                    serverHost: '0.0.0.0',
                    serverPort: '3000',
                    frontendHost: 'localhost',
                    frontendPort: '5000',
                    apiHost: 'localhost',
                    environment: 'development'
                };
                break;

            case '2':
                // 局域网访问模式
                if (localIPs.length === 0) {
                    log('❌ 未检测到可用的网络接口', 'red');
                    process.exit(1);
                }

                let selectedIP;
                if (localIPs.length === 1) {
                    selectedIP = localIPs[0].address;
                    log(`🔧 自动选择IP: ${selectedIP}`, 'green');
                } else {
                    const ipChoice = await question(`请选择IP地址 (1-${localIPs.length}): `);
                    const ipIndex = parseInt(ipChoice) - 1;
                    if (ipIndex >= 0 && ipIndex < localIPs.length) {
                        selectedIP = localIPs[ipIndex].address;
                    } else {
                        log('❌ 无效选择', 'red');
                        process.exit(1);
                    }
                }

                config = {
                    serverHost: '0.0.0.0',
                    serverPort: '3000',
                    frontendHost: selectedIP,
                    frontendPort: '5000',
                    apiHost: selectedIP,
                    environment: 'production'
                };
                break;

            case '3':
                // 自定义配置模式
                log('🔧 自定义配置模式', 'yellow');
                
                // 后端监听地址
                let serverHost;
                do {
                    serverHost = await question('后端监听地址 (0.0.0.0): ') || '0.0.0.0';
                    if (!isValidIP(serverHost)) {
                        log('❌ 无效的IP地址格式', 'red');
                    }
                } while (!isValidIP(serverHost));

                // 后端端口
                let serverPort;
                do {
                    serverPort = await question('后端端口 (3000): ') || '3000';
                    if (!isValidPort(serverPort)) {
                        log('❌ 无效的端口号 (1-65535)', 'red');
                    }
                } while (!isValidPort(serverPort));

                // 前端访问IP
                let apiHost;
                do {
                    apiHost = await question('前端连接IP (localhost): ') || 'localhost';
                    if (!isValidIP(apiHost)) {
                        log('❌ 无效的IP地址格式', 'red');
                    }
                } while (!isValidIP(apiHost));

                // 前端端口
                let frontendPort;
                do {
                    frontendPort = await question('前端端口 (5000): ') || '5000';
                    if (!isValidPort(frontendPort)) {
                        log('❌ 无效的端口号 (1-65535)', 'red');
                    }
                } while (!isValidPort(frontendPort));

                // 运行环境
                const env = await question('运行环境 (development/production) [development]: ') || 'development';

                config = {
                    serverHost,
                    serverPort,
                    frontendHost: apiHost,
                    frontendPort,
                    apiHost,
                    environment: env
                };
                break;

            default:
                log('❌ 无效选择', 'red');
                process.exit(1);
        }

        // 显示配置预览
        showConfigPreview(config);

        // 确认配置
        const confirm = await question('\n是否应用此配置? (y/N): ');
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            log('❌ 配置已取消', 'yellow');
            process.exit(0);
        }

        // 创建配置文件
        createEnvFile(config);

        // 显示后续步骤
        log('\n🎉 配置完成！', 'green');
        log('\n📋 后续步骤:', 'blue');
        log('1. 重启服务以应用新配置:', 'cyan');
        log('   npm run dev:full  # 开发环境', 'cyan');
        log('   npm run deploy    # 生产环境', 'cyan');
        log('');
        log('2. 访问地址:', 'cyan');
        log(`   前端: http://${config.frontendHost}:${config.frontendPort}`, 'cyan');
        log(`   API:  http://${config.apiHost}:${config.serverPort}`, 'cyan');
        log('');
        log('3. 验证配置:', 'cyan');
        log(`   curl http://${config.apiHost}:${config.serverPort}/api/health`, 'cyan');

    } catch (error) {
        log(`❌ 配置过程中发生错误: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 运行配置向导
setupConfiguration();
