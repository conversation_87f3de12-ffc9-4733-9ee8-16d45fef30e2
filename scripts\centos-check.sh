#!/bin/bash

# CentOS 环境检查脚本
# 检查 CentOS 系统是否满足部署要求

echo "🔍 检查 CentOS 环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_pass() {
    echo -e "${GREEN}✅ $1${NC}"
}

check_fail() {
    echo -e "${RED}❌ $1${NC}"
}

check_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

check_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查系统版本
echo "📋 系统信息检查..."
if [ -f /etc/centos-release ]; then
    CENTOS_VERSION=$(cat /etc/centos-release)
    check_pass "CentOS 系统: $CENTOS_VERSION"
elif [ -f /etc/redhat-release ]; then
    REDHAT_VERSION=$(cat /etc/redhat-release)
    check_pass "RedHat 系统: $REDHAT_VERSION"
else
    check_warn "未检测到 CentOS/RedHat 系统，但可能仍然兼容"
fi

# 检查架构
ARCH=$(uname -m)
check_info "系统架构: $ARCH"

# 检查内存
MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
MEMORY_MB=$(free -m | awk '/^Mem:/{print $2}')
if [ $MEMORY_MB -ge 512 ]; then
    check_pass "内存: ${MEMORY_MB}MB (推荐 >= 512MB)"
else
    check_warn "内存: ${MEMORY_MB}MB (推荐至少 512MB)"
fi

# 检查磁盘空间
DISK_SPACE=$(df -h / | awk 'NR==2{print $4}')
DISK_SPACE_MB=$(df -m / | awk 'NR==2{print $4}')
if [ $DISK_SPACE_MB -ge 1024 ]; then
    check_pass "磁盘空间: $DISK_SPACE 可用"
else
    check_warn "磁盘空间: $DISK_SPACE 可用 (推荐至少 1GB)"
fi

# 检查 Node.js
echo ""
echo "📦 软件依赖检查..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'v' -f2 | cut -d'.' -f1)
    if [ $NODE_MAJOR -ge 16 ]; then
        check_pass "Node.js: $NODE_VERSION"
    else
        check_fail "Node.js 版本过低: $NODE_VERSION (需要 >= 16.0)"
        echo "安装命令："
        echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
        echo "sudo yum install -y nodejs"
    fi
else
    check_fail "Node.js 未安装"
    echo "安装命令："
    echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
    echo "sudo yum install -y nodejs"
fi

# 检查 npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm -v)
    check_pass "npm: $NPM_VERSION"
else
    check_fail "npm 未安装 (通常随 Node.js 一起安装)"
fi

# 检查端口占用
echo ""
echo "🔌 端口检查..."
check_port() {
    local port=$1
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        check_warn "端口 $port 已被占用"
        echo "   占用进程: $(netstat -tulnp 2>/dev/null | grep ":$port " | awk '{print $7}' | head -1)"
    else
        check_pass "端口 $port 可用"
    fi
}

check_port 3000
check_port 5000

# 检查防火墙
echo ""
echo "🔥 防火墙检查..."
if systemctl is-active --quiet firewalld; then
    check_info "firewalld 正在运行"
    if firewall-cmd --list-ports | grep -q "3000/tcp"; then
        check_pass "端口 3000 已开放"
    else
        check_warn "端口 3000 未开放"
        echo "   开放命令: sudo firewall-cmd --permanent --add-port=3000/tcp"
    fi
    if firewall-cmd --list-ports | grep -q "5000/tcp"; then
        check_pass "端口 5000 已开放"
    else
        check_warn "端口 5000 未开放"
        echo "   开放命令: sudo firewall-cmd --permanent --add-port=5000/tcp"
    fi
else
    check_info "firewalld 未运行或未安装"
fi

# 检查 SELinux
echo ""
echo "🛡️  SELinux 检查..."
if command -v getenforce &> /dev/null; then
    SELINUX_STATUS=$(getenforce)
    if [ "$SELINUX_STATUS" = "Enforcing" ]; then
        check_warn "SELinux 处于强制模式，可能需要配置策略"
    elif [ "$SELINUX_STATUS" = "Permissive" ]; then
        check_info "SELinux 处于宽松模式"
    else
        check_pass "SELinux 已禁用"
    fi
else
    check_info "SELinux 未安装"
fi

# 检查网络连接
echo ""
echo "🌐 网络检查..."
if ping -c 1 8.8.8.8 &> /dev/null; then
    check_pass "网络连接正常"
else
    check_warn "网络连接可能有问题"
fi

# 检查用户权限
echo ""
echo "👤 用户权限检查..."
if [ "$EUID" -eq 0 ]; then
    check_warn "当前为 root 用户，建议使用普通用户运行应用"
else
    check_pass "当前用户: $(whoami)"
fi

# 检查 systemd
if systemctl --version &> /dev/null; then
    check_pass "systemd 可用 (支持服务管理)"
else
    check_warn "systemd 不可用"
fi

# 总结
echo ""
echo "📋 检查总结:"
echo "─────────────────────────────────────"

# 必需项检查
REQUIRED_OK=true

if ! command -v node &> /dev/null; then
    REQUIRED_OK=false
    check_fail "必需: Node.js 未安装"
elif [ $(node -v | cut -d'v' -f2 | cut -d'.' -f1) -lt 16 ]; then
    REQUIRED_OK=false
    check_fail "必需: Node.js 版本过低"
fi

if ! command -v npm &> /dev/null; then
    REQUIRED_OK=false
    check_fail "必需: npm 未安装"
fi

if [ $MEMORY_MB -lt 256 ]; then
    REQUIRED_OK=false
    check_fail "必需: 内存不足 256MB"
fi

if [ $REQUIRED_OK = true ]; then
    check_pass "✅ 系统满足基本部署要求"
    echo ""
    echo "🚀 可以开始部署网络调试助手！"
    echo ""
    echo "📦 部署步骤："
    echo "1. 上传部署包到服务器"
    echo "2. tar -xzf network-debug-helper-*-centos.tar.gz"
    echo "3. cd network-debug-helper-*"
    echo "4. chmod +x install.sh && ./install.sh"
    echo "5. npm start"
else
    check_fail "❌ 系统不满足部署要求，请先解决上述问题"
    exit 1
fi
