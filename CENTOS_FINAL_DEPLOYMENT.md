# 🐧 CentOS 最终部署指南

## 🎉 问题已解决！

您遇到的 `npm ERR! Missing script: "setup:ip"` 错误已经在新版本的部署包中修复。

## 📦 新版本特性

### ✅ 包含的配置工具
- **Node.js 配置向导**: `npm run setup:ip`
- **Shell 配置脚本**: `npm run setup:ip:shell`
- **配置查看**: `npm run config:show`
- **完整配置文档**: `IP_CONFIG_GUIDE.md`

### ✅ 包含的文件
- `scripts/setup-ip.js` - Node.js 配置向导
- `scripts/centos-setup-ip.sh` - Shell 配置脚本
- `config/app.config.js` - 应用配置管理
- `.env.example` - 配置示例文件
- `IP_CONFIG_GUIDE.md` - 详细配置指南

## 🚀 部署步骤

### 1. 重新下载部署包

```bash
# 下载新版本的部署包
# network-debug-helper-0.0.0-centos.tar.gz (0.38 MB)
```

### 2. 解压并安装

```bash
# 解压
tar -xzf network-debug-helper-0.0.0-centos.tar.gz
cd network-debug-helper-0.0.0

# 运行安装脚本
chmod +x install.sh
./install.sh
```

### 3. 配置IP地址

现在您有多种配置方式：

#### 方式一：Shell 配置脚本（推荐）
```bash
npm run setup:ip:shell
```

#### 方式二：Node.js 配置向导
```bash
npm run setup:ip
```

#### 方式三：手动配置
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置
vi .env
```

### 4. 启动应用

```bash
# 启动服务
npm start

# 验证部署
npm run check

# 查看配置
npm run config:show
```

## 🔧 配置示例

### 本地访问
```bash
# 运行配置向导，选择模式 1
npm run setup:ip:shell
# 选择: 1. 本地开发 (localhost)
```

### 局域网访问
```bash
# 运行配置向导，选择模式 2
npm run setup:ip:shell
# 选择: 2. 局域网访问 (自动检测IP)
```

### 自定义配置
```bash
# 运行配置向导，选择模式 3
npm run setup:ip:shell
# 选择: 3. 自定义配置
# 然后按提示输入IP和端口
```

## 📋 可用命令

```bash
npm start                # 启动应用
npm run check           # 检查部署状态
npm run setup:ip        # Node.js 配置向导
npm run setup:ip:shell  # Shell 配置向导
npm run config:show     # 显示当前配置
```

## 🎯 快速配置场景

### 场景1: 服务器IP为 *************
```bash
# 使用配置向导
npm run setup:ip:shell
# 选择模式 2 或 3，设置IP为 *************
```

### 场景2: 自定义端口
```bash
# 使用配置向导
npm run setup:ip:shell
# 选择模式 3，自定义端口配置
```

## 🔍 验证部署

```bash
# 检查服务状态
curl http://YOUR_SERVER_IP:3000/api/health

# 访问前端
# 浏览器打开: http://YOUR_SERVER_IP:5000
```

## 📖 详细文档

部署包中包含完整的配置文档：

- `IP_CONFIG_GUIDE.md` - 详细IP配置指南
- `CENTOS_DEPLOY_GUIDE.md` - CentOS部署指南
- `.env.example` - 配置示例文件

## 🛠️ 故障排除

### 1. 如果仍然提示脚本不存在
```bash
# 检查文件是否存在
ls -la scripts/
ls -la package.json

# 手动运行Shell脚本
chmod +x scripts/centos-setup-ip.sh
./scripts/centos-setup-ip.sh
```

### 2. 权限问题
```bash
# 给脚本执行权限
chmod +x scripts/centos-setup-ip.sh
chmod +x install.sh
```

### 3. 配置不生效
```bash
# 检查配置文件
cat .env

# 重启服务
npm start
```

## 💡 最佳实践

1. **使用配置向导**: 推荐使用 `npm run setup:ip:shell`
2. **验证配置**: 配置后立即测试访问
3. **备份配置**: 保存工作的配置文件
4. **查看日志**: 如有问题查看服务启动日志

## 🎉 部署成功

配置完成后，您将能够：

- ✅ 通过配置的IP地址访问前端界面
- ✅ 使用TCP客户端和服务器功能
- ✅ 查看美化的数据日志
- ✅ 实时监控连接状态

**访问地址**: http://您配置的IP:5000

现在您可以开始使用网络调试助手进行TCP网络调试了！
