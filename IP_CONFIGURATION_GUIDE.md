# IP地址配置指南

## 🌐 概述

网络调试助手现在支持自定义IP地址配置，您可以在部署时指定项目运行的IP地址，而不是依赖系统自动识别。

## 🔧 配置方式

### 方式一：配置文件预设（推荐）

在部署前修改 `config.json` 文件：

```json
{
  "server": {
    "host": "0.0.0.0",
    "backend_port": 3000,
    "frontend_port": 5000
  },
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "*************",
    "description": "如果 auto_detect_ip 为 false，将使用 custom_ip 作为服务器IP地址"
  }
}
```

**配置说明：**
- `auto_detect_ip`: `true` 自动检测，`false` 使用自定义IP
- `custom_ip`: 自定义IP地址（当 `auto_detect_ip` 为 `false` 时生效）
- `host`: 服务绑定的主机地址（通常保持 `0.0.0.0`）

### 方式二：部署时交互配置

运行部署脚本时，系统会询问是否使用自定义IP：

```bash
./deploy.sh
```

部署过程中会显示：
```
当前检测到的服务器IP: ************
是否要使用自定义IP地址? (y/N): y
请输入自定义IP地址: *************
使用自定义IP地址: *************
```

## 📋 使用场景

### 场景一：多网卡服务器
当服务器有多个网卡时，系统可能检测到内网IP，但您希望使用公网IP：
```json
{
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "************"
  }
}
```

### 场景二：Docker容器部署
在容器环境中，您可能需要指定特定的IP地址：
```json
{
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "**********"
  }
}
```

### 场景三：负载均衡环境
在负载均衡器后面，您可能需要指定负载均衡器的IP：
```json
{
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "**********"
  }
}
```

### 场景四：域名访问
如果您有域名，可以使用域名作为"IP"：
```json
{
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "your-domain.com"
  }
}
```

## 🔍 IP地址检测逻辑

### 自动检测（默认）
```bash
# 系统会执行以下命令获取IP
hostname -I | awk '{print $1}'
```

### 配置文件检测
1. 检查 `config.json` 是否存在
2. 读取 `auto_detect_ip` 设置
3. 如果为 `false`，使用 `custom_ip`
4. 如果为 `true` 或未设置，自动检测

### 交互式配置
1. 显示当前检测到的IP
2. 询问是否使用自定义IP
3. 如果选择是，提示输入自定义IP
4. 验证并使用输入的IP

## 🚀 部署示例

### 示例一：使用公网IP
```bash
# 1. 修改配置文件
cat > config.json << EOF
{
  "server": {
    "host": "0.0.0.0",
    "backend_port": 3000,
    "frontend_port": 5000
  },
  "deployment": {
    "auto_detect_ip": false,
    "custom_ip": "************"
  }
}
EOF

# 2. 运行部署
./deploy.sh
```

### 示例二：交互式配置
```bash
./deploy.sh
# 当询问时输入自定义IP
```

## 🔧 环境变量

部署脚本会设置以下环境变量：
- `HOST`: 服务器IP地址
- `PORT`: 后端端口（默认3000）
- `FRONTEND_PORT`: 前端端口（默认5000）

这些变量会传递给PM2进程，确保服务使用正确的IP地址。

## 📊 验证配置

### 检查服务状态
```bash
pm2 status
```

### 检查端口监听
```bash
netstat -tuln | grep :3000
netstat -tuln | grep :5000
```

### 测试访问
```bash
# 测试后端API
curl http://YOUR_IP:3000/api/health

# 测试前端页面
curl http://YOUR_IP:5000
```

## 🐛 故障排除

### 问题一：IP地址无法访问
**原因**: 防火墙阻止或IP地址不正确
**解决**: 
```bash
# 检查防火墙
sudo firewall-cmd --list-ports
# 开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload
```

### 问题二：服务启动失败
**原因**: IP地址格式错误或不可用
**解决**: 
```bash
# 检查IP格式
ping YOUR_IP
# 重新配置
vim config.json
./deploy.sh
```

### 问题三：无法绑定到指定IP
**原因**: IP地址不属于本机
**解决**: 
- 确认IP地址是否正确
- 使用 `0.0.0.0` 作为绑定地址
- 检查网络配置

## 💡 最佳实践

### 1. 生产环境
- 使用配置文件预设IP地址
- 使用公网IP或负载均衡器IP
- 确保防火墙正确配置

### 2. 开发环境
- 可以使用自动检测
- 使用内网IP即可
- 便于团队访问

### 3. 容器环境
- 明确指定容器IP
- 考虑端口映射
- 注意网络模式

### 4. 安全考虑
- 不要在配置文件中暴露敏感IP
- 使用防火墙限制访问
- 考虑使用HTTPS（需要额外配置）

## 📚 相关文档

- [部署指南](./DEPLOYMENT_GUIDE.md)
- [PM2配置修复](./PM2_FIX_GUIDE.md)
- [README](./README.md)

通过灵活的IP配置功能，您可以在各种网络环境中轻松部署网络调试助手！
