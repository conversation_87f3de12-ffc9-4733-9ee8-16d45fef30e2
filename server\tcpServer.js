import net from 'net';

/**
 * TCP 服务器处理类
 */
class TCPServer {
    constructor(connectionManager) {
        this.connectionManager = connectionManager;
    }

    /**
     * 启动 TCP 服务器
     */
    async start(host, port) {
        return new Promise((resolve, reject) => {
            try {
                const server = net.createServer();

                server.on('connection', (socket) => {
                    this.handleClientConnection(socket);
                });

                server.on('error', (error) => {
                    console.error('TCP 服务器错误:', error);

                    let errorMessage = '服务器启动失败';
                    if (error.code === 'EADDRINUSE') {
                        errorMessage = `端口 ${port} 已被占用`;
                    } else if (error.code === 'EACCES') {
                        errorMessage = `没有权限使用端口 ${port}`;
                    } else {
                        errorMessage = `端口错误: ${error.message}`;
                    }

                    this.connectionManager.sendError(errorMessage);
                    reject(new Error(errorMessage));
                });

                server.listen(port, host, () => {
                    console.log(`TCP 服务器已启动，监听 ${host}:${port}`);
                    this.connectionManager.serverSocket = server;
                    this.connectionManager.isServerRunning = true;

                    // 发送服务器启动状态
                    this.connectionManager.sendConnectionStatus('connected', {
                        type: 'server',
                        host: host,
                        port: port
                    });

                    resolve();
                });

            } catch (error) {
                console.error('启动 TCP 服务器时出错:', error);
                reject(error);
            }
        });
    }

    /**
     * 处理客户端连接
     */
    handleClientConnection(socket) {
        const clientInfo = `${socket.remoteAddress}:${socket.remotePort}`;
        console.log(`新客户端连接: ${clientInfo}`);

        // 添加到客户端连接集合
        this.connectionManager.clientSockets.add(socket);

        // 发送客户端连接状态
        this.connectionManager.sendConnectionStatus('connected', {
            type: 'client_to_server',
            host: socket.remoteAddress,
            port: socket.remotePort,
            clientInfo: clientInfo
        });

        // 处理数据接收
        socket.on('data', (data) => {
            console.log(`从 ${clientInfo} 接收到数据:`, data.toString());
            this.connectionManager.handleReceivedData(data, clientInfo);
        });

        // 处理连接关闭
        socket.on('close', () => {
            console.log(`客户端断开连接: ${clientInfo}`);
            this.connectionManager.clientSockets.delete(socket);

            // 发送客户端断开状态
            this.connectionManager.sendConnectionStatus('disconnected', {
                type: 'client_to_server',
                host: socket.remoteAddress,
                port: socket.remotePort,
                clientInfo: clientInfo
            });
        });

        // 处理连接错误
        socket.on('error', (error) => {
            console.error(`客户端连接错误 ${clientInfo}:`, error);
            this.connectionManager.clientSockets.delete(socket);

            // 发送客户端断开状态
            this.connectionManager.sendConnectionStatus('disconnected', {
                type: 'client_to_server',
                host: socket.remoteAddress,
                port: socket.remotePort,
                clientInfo: clientInfo
            });
        });
    }

    /**
     * 向所有连接的客户端发送数据
     */
    sendToAllClients(data) {
        // data 现在可能是 Buffer 或字符串
        const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf-8');
        let successCount = 0;

        for (const clientSocket of this.connectionManager.clientSockets) {
            try {
                if (!clientSocket.destroyed) {
                    clientSocket.write(buffer);
                    successCount++;
                }
            } catch (error) {
                console.error('向客户端发送数据失败:', error);
                // 移除已断开的连接
                this.connectionManager.clientSockets.delete(clientSocket);
            }
        }

        return successCount > 0;
    }
}

export default TCPServer;
