@echo off
chcp 65001 >nul

REM 网络调试助手 CentOS 打包脚本 (Windows 版本)
REM 创建可在 CentOS 上部署的完整包

echo 🚀 开始打包网络调试助手 for CentOS...

REM 获取版本号
for /f "tokens=*" %%i in ('node -p "require('./package.json').version"') do set VERSION=%%i

REM 配置
set PACKAGE_NAME=network-debug-helper
set BUILD_DIR=build
set PACKAGE_DIR=%BUILD_DIR%\%PACKAGE_NAME%-%VERSION%
set ARCHIVE_NAME=%PACKAGE_NAME%-%VERSION%-centos.tar.gz

REM 清理之前的构建
echo 🧹 清理之前的构建...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
mkdir %PACKAGE_DIR%

REM 安装依赖
echo 📦 安装依赖...
npm ci --production=false
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 构建前端
echo 🔨 构建前端...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

REM 复制必要文件到打包目录
echo 📋 复制文件...

REM 复制构建后的前端文件
xcopy /e /i dist %PACKAGE_DIR%\dist

REM 复制后端源码
xcopy /e /i server %PACKAGE_DIR%\server

REM 复制配置文件
copy package.json %PACKAGE_DIR%\
copy package-lock.json %PACKAGE_DIR%\

REM 复制文档
copy README.md %PACKAGE_DIR%\
copy CENTOS_DEPLOY_GUIDE.md %PACKAGE_DIR%\
copy check-deployment.js %PACKAGE_DIR%\

REM 创建生产环境的 package.json
echo 📝 创建生产环境 package.json...
node -e "const pkg = require('./package.json'); const prodPkg = { name: pkg.name, version: pkg.version, description: pkg.description, type: pkg.type, scripts: { 'start': 'node server/server.js', 'check': 'node check-deployment.js' }, dependencies: { 'express': pkg.dependencies.express, 'ws': pkg.dependencies.ws, 'cors': pkg.dependencies.cors } }; require('fs').writeFileSync('%PACKAGE_DIR%/package.json', JSON.stringify(prodPkg, null, 2));"

REM 创建安装脚本
echo 📜 创建安装脚本...
(
echo #!/bin/bash
echo.
echo # CentOS 安装脚本
echo set -e
echo.
echo echo "🚀 开始在 CentOS 上安装网络调试助手..."
echo.
echo # 检查 Node.js
echo echo "📋 检查 Node.js..."
echo if ! command -v node ^&^> /dev/null; then
echo     echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
echo     echo "安装命令："
echo     echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
echo     echo "sudo yum install -y nodejs"
echo     exit 1
echo fi
echo.
echo NODE_VERSION=$^(node -v ^| cut -d'v' -f2 ^| cut -d'.' -f1^)
echo if [ "$NODE_VERSION" -lt 16 ]; then
echo     echo "❌ Node.js 版本过低，需要 16+ 版本，当前版本: $^(node -v^)"
echo     exit 1
echo fi
echo.
echo echo "✅ Node.js 版本: $^(node -v^)"
echo.
echo # 安装依赖
echo echo "📦 安装依赖..."
echo npm install --production
echo.
echo echo "✅ 安装完成！"
echo echo ""
echo echo "🚀 启动应用："
echo echo "npm start"
echo echo ""
echo echo "🔍 检查状态："
echo echo "npm run check"
echo echo ""
echo echo "🌐 访问地址："
echo echo "http://localhost:5000"
) > %PACKAGE_DIR%\install.sh

REM 创建 systemd 服务文件
(
echo [Unit]
echo Description=Network Debug Helper
echo After=network.target
echo.
echo [Service]
echo Type=simple
echo User=nodejs
echo WorkingDirectory=/opt/network-debug-helper
echo ExecStart=/usr/bin/node server/server.js
echo Restart=always
echo RestartSec=10
echo Environment=NODE_ENV=production
echo Environment=PORT=3000
echo Environment=HOST=0.0.0.0
echo.
echo # 日志
echo StandardOutput=journal
echo StandardError=journal
echo SyslogIdentifier=network-debug-helper
echo.
echo [Install]
echo WantedBy=multi-user.target
) > %PACKAGE_DIR%\network-debug-helper.service

REM 创建部署说明
(
echo # CentOS 部署指南
echo.
echo ## 快速部署
echo.
echo ### 1. 解压文件
echo ```bash
echo tar -xzf network-debug-helper-*-centos.tar.gz
echo cd network-debug-helper-*
echo ```
echo.
echo ### 2. 运行安装脚本
echo ```bash
echo chmod +x install.sh
echo ./install.sh
echo ```
echo.
echo ### 3. 启动应用
echo ```bash
echo npm start
echo ```
echo.
echo ### 4. 验证部署
echo ```bash
echo npm run check
echo ```
echo.
echo ## 访问地址
echo.
echo - 前端界面: http://localhost:5000
echo - 后端 API: http://localhost:3000
echo.
echo ## 生产环境部署
echo.
echo ### 使用 systemd 服务
echo.
echo 1. 复制到系统目录：
echo ```bash
echo sudo cp -r . /opt/network-debug-helper
echo sudo chown -R nodejs:nodejs /opt/network-debug-helper
echo ```
echo.
echo 2. 安装服务：
echo ```bash
echo sudo cp network-debug-helper.service /etc/systemd/system/
echo sudo systemctl daemon-reload
echo sudo systemctl enable network-debug-helper
echo sudo systemctl start network-debug-helper
echo ```
echo.
echo 3. 检查状态：
echo ```bash
echo sudo systemctl status network-debug-helper
echo ```
) > %PACKAGE_DIR%\CENTOS_DEPLOY.md

REM 检查是否有 tar 命令 (Git Bash 或 WSL)
where tar >nul 2>nul
if %errorlevel% equ 0 (
    echo 📦 创建 tar.gz 压缩包...
    cd %BUILD_DIR%
    tar -czf %ARCHIVE_NAME% %PACKAGE_NAME%-%VERSION%
    cd ..
    move %BUILD_DIR%\%ARCHIVE_NAME% .
) else (
    echo ⚠️  未找到 tar 命令，创建 zip 压缩包...
    powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%PACKAGE_NAME%-%VERSION%-centos.zip' -Force"
    set ARCHIVE_NAME=%PACKAGE_NAME%-%VERSION%-centos.zip
)

echo ✅ 打包完成！
echo.
echo 📦 打包文件: %ARCHIVE_NAME%
for %%A in (%ARCHIVE_NAME%) do echo 📁 大小: %%~zA bytes
echo.
echo 🚀 CentOS 部署步骤：
echo 1. 上传 %ARCHIVE_NAME% 到 CentOS 服务器
if "%ARCHIVE_NAME:~-7%"==".tar.gz" (
    echo 2. tar -xzf %ARCHIVE_NAME%
) else (
    echo 2. unzip %ARCHIVE_NAME%
)
echo 3. cd %PACKAGE_NAME%-%VERSION%
echo 4. chmod +x install.sh ^&^& ./install.sh
echo 5. npm start
echo.
echo 🌐 访问地址: http://服务器IP:5000

pause
