# 网络调试助手环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# ================================
# 后端服务器配置
# ================================

# 后端服务器监听地址
# 0.0.0.0 表示监听所有网络接口
# 127.0.0.1 或 localhost 表示只监听本地
# 指定IP地址表示只监听该IP
SERVER_HOST=0.0.0.0

# 后端服务器端口
SERVER_PORT=3000

# 生产环境后端配置
PROD_SERVER_HOST=0.0.0.0
PROD_SERVER_PORT=3000

# ================================
# 前端配置
# ================================

# 开发环境前端端口
CLIENT_DEV_PORT=5174

# 生产环境前端端口
CLIENT_PROD_PORT=5000

# 前端监听地址 (true 表示监听所有地址)
CLIENT_HOST=true

# ================================
# API 连接配置
# ================================

# 开发环境 API 基础地址
# 如果前端和后端在不同服务器，修改为后端服务器的实际IP
API_BASE_URL=http://localhost:3000

# 开发环境 WebSocket 连接地址
WS_URL=ws://localhost:3000/ws

# 生产环境 API 配置
# 部署时修改为实际的服务器IP地址
PROD_API_BASE_URL=http://localhost:3000
PROD_WS_URL=ws://localhost:3000/ws

# ================================
# 运行环境
# ================================

# 运行环境 (development | production)
NODE_ENV=development

# ================================
# 示例配置场景
# ================================

# 场景1: 本地开发 (默认)
# SERVER_HOST=0.0.0.0
# SERVER_PORT=3000
# API_BASE_URL=http://localhost:3000
# WS_URL=ws://localhost:3000/ws

# 场景2: 局域网访问
# SERVER_HOST=0.0.0.0
# SERVER_PORT=3000
# API_BASE_URL=http://*************:3000
# WS_URL=ws://*************:3000/ws

# 场景3: 生产环境部署
# NODE_ENV=production
# PROD_SERVER_HOST=0.0.0.0
# PROD_SERVER_PORT=3000
# PROD_API_BASE_URL=http://your-server-ip:3000
# PROD_WS_URL=ws://your-server-ip:3000/ws

# 场景4: 不同端口配置
# SERVER_PORT=8080
# CLIENT_PROD_PORT=8000
# API_BASE_URL=http://localhost:8080
# WS_URL=ws://localhost:8080/ws
