# PM2 配置文件修复指南

## 🐛 问题描述

在部署过程中遇到以下错误：
```
[PM2][ERROR] File ecosystem.config.js malformated
Error [ERR_REQUIRE_ESM]: require() of ES Module ecosystem.config.js not supported.
```

## 🔍 问题原因

由于项目的 `package.json` 中设置了 `"type": "module"`，这使得所有 `.js` 文件都被视为 ES 模块。但是 PM2 需要 CommonJS 格式的配置文件，不支持 ES 模块格式的配置文件。

## ✅ 解决方案

### 方案一：使用 .cjs 扩展名（已实施）

将 `ecosystem.config.js` 重命名为 `ecosystem.config.cjs`，这样 PM2 就会将其识别为 CommonJS 模块。

**修改的文件：**
1. `ecosystem.config.js` → `ecosystem.config.cjs`
2. `package.json` - 更新脚本引用
3. `scripts/package.js` - 更新打包脚本
4. `deploy.sh` - 更新部署脚本
5. `scripts/test-deployment.js` - 更新测试脚本
6. `README.md` - 更新文档

### 方案二：修改 package.json（备选）

如果不想使用 `.cjs` 扩展名，也可以在生产环境的 `package.json` 中移除 `"type": "module"`，但这会影响后端代码的模块系统。

## 🔧 修复后的使用方法

### 本地开发
```bash
npm run pm2:start    # 使用 ecosystem.config.cjs
pm2 status
pm2 logs
```

### 生产部署
```bash
# 自动部署（推荐）
./deploy.sh

# 手动部署
pm2 start ecosystem.config.cjs
pm2 save
pm2 startup
```

## 📋 验证修复

### 1. 检查配置文件
```bash
ls -la ecosystem.config.cjs
```

### 2. 测试 PM2 启动
```bash
pm2 start ecosystem.config.cjs
pm2 status
```

### 3. 检查服务运行
```bash
pm2 logs network-debug-helper
pm2 logs network-debug-helper-frontend
```

### 4. 验证端口监听
```bash
netstat -tuln | grep :3000
netstat -tuln | grep :5000
```

## 🎯 预期结果

修复后，PM2 应该能够正常启动两个服务：
- `network-debug-helper` (后端服务，端口 3000)
- `network-debug-helper-frontend` (前端服务，端口 5000)

## 🔄 如果仍有问题

### 检查 Node.js 版本
```bash
node --version  # 需要 >= 16.0.0
```

### 检查 PM2 版本
```bash
pm2 --version   # 建议 >= 5.0.0
```

### 手动测试服务
```bash
# 测试后端
node server/server.js

# 测试前端（另一个终端）
node server/static-server.js
```

### 清理 PM2 进程
```bash
pm2 delete all
pm2 kill
pm2 start ecosystem.config.cjs
```

## 📚 相关文档

- [PM2 Configuration File](https://pm2.keymetrics.io/docs/usage/application-declaration/)
- [Node.js ES Modules](https://nodejs.org/api/esm.html)
- [CommonJS vs ES Modules](https://nodejs.org/api/modules.html)

## ✨ 总结

通过将 PM2 配置文件重命名为 `.cjs` 扩展名，我们解决了 ES 模块与 CommonJS 的兼容性问题。现在部署包可以在 Linux 服务器上正常运行，支持：

- ✅ 自动化部署脚本
- ✅ PM2 进程管理
- ✅ 双服务架构（前端+后端）
- ✅ 日志管理和监控
- ✅ 开机自启动

部署包已经过测试，可以安全部署到生产环境！
