#!/bin/bash

# 网络调试助手部署脚本
echo "🚀 开始部署网络调试助手..."

# 检查 Node.js 版本
echo "📋 检查环境..."
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 安装依赖
echo "📦 安装依赖..."
npm install

# 构建前端
echo "🔨 构建前端..."
npm run build

# 检查构建结果
if [ -d "dist" ]; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi

# 启动服务
echo "🌟 启动服务..."
echo "前端将在 http://localhost:5000 运行"
echo "后端 API 将在 http://localhost:3000 运行"
echo ""
echo "按 Ctrl+C 停止服务"

# 设置生产环境并启动
NODE_ENV=production npm run start
