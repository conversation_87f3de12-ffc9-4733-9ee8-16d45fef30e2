# 网络调试助手 - 部署包

## 快速启动

### Linux/macOS
```bash
chmod +x start.sh
./start.sh
```

### Windows
```cmd
start.bat
```

## 手动启动
```bash
npm install --production
node server/server.js
```

## 配置说明

编辑 `config.json` 文件来修改服务器配置：

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 5000
  }
}
```

## 访问地址

服务启动后，在浏览器中访问：
- http://localhost:5000 (本地访问)
- http://服务器IP:5000 (远程访问)

## 系统要求

- Node.js 16.0 或更高版本
- 支持的操作系统：Linux、macOS、Windows
