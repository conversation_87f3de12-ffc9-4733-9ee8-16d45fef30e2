# 网络调试助手

一个基于 Vue 3 + Node.js 的网络调试工具，支持 TCP 客户端和服务器模式，提供实时数据收发功能。

## 🌟 功能特性

- **TCP 客户端模式**: 连接到远程 TCP 服务器
- **TCP 服务器模式**: 监听端口，接受客户端连接
- **实时数据传输**: WebSocket 实现的实时数据收发
- **多种数据格式**: 支持 ASCII 和 HEX 格式的独立收发设置
- **美化数据日志**: 颜色区分、图标标识的结构化日志显示
- **连接状态监控**: 实时显示连接建立和断开状态
- **数据统计**: 实时显示收发字节数
- **数据保存**: 支持将接收数据保存为文件
- **响应式设计**: 支持桌面和移动端访问

## 🛠️ 技术栈

- **前端**: Vue 3 + TypeScript + Element Plus + Tailwind CSS
- **后端**: Node.js + Express + WebSocket
- **构建工具**: Vite
- **进程管理**: PM2
- **部署**: Linux 系统自动化部署

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd network-debug-helper

# 安装依赖
npm install

# 开发模式 - 同时启动前端和后端
npm run dev:full

# 或者分别启动
npm run dev      # 启动前端 (端口 5174)
npm run server   # 启动后端 (端口 3000)
```

访问 http://localhost:5174 开始使用

## 📦 生产部署

### 方式一：自动化部署（推荐）

1. **构建部署包**
```bash
npm run build:prod
```

2. **上传到 Linux 服务器**
```bash
# 将生成的 network-debug-helper-deploy.tar.gz 上传到服务器
scp network-debug-helper-deploy.tar.gz user@your-server:/path/to/deploy/
```

3. **在服务器上部署**
```bash
# 解压部署包
tar -xzf network-debug-helper-deploy.tar.gz
cd network-debug-helper

# 运行自动部署脚本
chmod +x deploy.sh
./deploy.sh
```

4. **访问应用**
- 前端地址: http://your-server-ip:5000
- 后端地址: http://your-server-ip:3000

### 方式二：手动部署

1. **构建前端**
```bash
npm run build
```

2. **安装生产依赖**
```bash
npm install --production
```

3. **使用 PM2 启动**
```bash
# 安装 PM2
npm install -g pm2

# 启动服务
pm2 start ecosystem.config.cjs

# 保存配置并设置开机自启
pm2 save
pm2 startup
```

## 🔧 服务管理

### PM2 常用命令

```bash
# 查看服务状态
pm2 status

# 查看实时日志
pm2 logs                              # 所有服务日志
pm2 logs network-debug-helper        # 后端日志
pm2 logs network-debug-helper-frontend # 前端日志

# 重启服务
pm2 restart network-debug-helper     # 重启后端
pm2 restart all                      # 重启所有服务

# 停止服务
pm2 stop network-debug-helper        # 停止后端
pm2 stop all                         # 停止所有服务

# 删除服务
pm2 delete network-debug-helper      # 删除后端服务
pm2 delete all                       # 删除所有服务
```

### 日志管理

日志文件位置：
- 后端日志: `./logs/combined.log`
- 前端日志: `./logs/frontend-combined.log`
- 错误日志: `./logs/err.log`

```bash
# 实时查看日志
tail -f logs/combined.log

# 查看最近的日志
tail -n 100 logs/combined.log
```

## 🎯 使用说明

### 基本操作

1. **选择协议类型**: TCP Client 或 TCP Server
2. **配置连接参数**: IP 地址和端口
3. **设置数据格式**:
   - 发送设置: 选择发送数据的格式（ASCII/HEX）
   - 接收设置: 选择接收数据的显示格式（ASCII/HEX）
4. **建立连接**: 点击连接按钮
5. **收发数据**: 在发送区输入数据并发送

### 数据格式说明

- **ASCII 模式**: 发送/显示普通文本
- **HEX 模式**: 发送/显示十六进制数据
  - 格式: `48 5A 00 01` 或 `485A0001`
  - 要求: 只能包含 0-9 和 A-F，字符数必须为偶数

### 美化日志功能

- 🟢 **绿色**: 连接成功状态
- 🔴 **红色**: 连接断开状态
- 🔵 **蓝色**: 系统信息
- 🟣 **紫色**: 接收数据
- 🟢 **翠绿**: 发送数据

## 📁 项目结构

```
├── src/                    # 前端源码
│   ├── components/         # Vue 组件
│   ├── assets/            # 静态资源
│   └── App.vue            # 主应用组件
├── server/                # 后端源码
│   ├── server.js          # 主服务器文件
│   ├── static-server.js   # 静态文件服务器
│   ├── connectionManager.js # 连接管理
│   ├── tcpServer.js       # TCP 服务器
│   └── tcpClient.js       # TCP 客户端
├── scripts/               # 构建脚本
│   └── package.js         # 打包脚本
├── deploy.sh              # 部署脚本
├── ecosystem.config.cjs   # PM2 配置
└── package.json           # 项目配置
```

## 🌐 API 接口

### HTTP 接口
- `POST /api/connect` - 建立连接
- `POST /api/disconnect` - 断开连接
- `POST /api/send` - 发送数据
- `GET /api/health` - 健康检查

### WebSocket 接口
- `WebSocket /ws` - 实时数据通信

## 🔧 配置说明

### 端口配置
- 前端服务: 5000 端口
- 后端服务: 3000 端口
- 可通过环境变量修改:
  ```bash
  export PORT=3000           # 后端端口
  export FRONTEND_PORT=5000  # 前端端口
  ```

### 系统要求
- **操作系统**: CentOS 7+, Ubuntu 18.04+, Debian 9+
- **内存**: 最小 512MB，推荐 1GB+
- **磁盘**: 最小 100MB 可用空间
- **网络**: 需要开放 3000 和 5000 端口

## 🐛 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
lsof -i :3000
lsof -i :5000

# 停止 PM2 服务
pm2 stop all
```

2. **Node.js 版本过低**
```bash
# 更新 Node.js
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo yum install -y nodejs
```

3. **权限问题**
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 如果需要 sudo 权限安装 Node.js
sudo ./deploy.sh
```

4. **防火墙设置**
```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian
sudo ufw allow 3000
sudo ufw allow 5000
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请查看：
1. [使用指南](./USAGE_GUIDE.md)
2. [连接状态功能说明](./CONNECTION_STATUS_GUIDE.md)
3. [美化日志功能说明](./BEAUTIFIED_LOG_GUIDE.md)
