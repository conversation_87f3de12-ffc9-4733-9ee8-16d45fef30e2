version: '3.8'

services:
  network-debug-helper:
    build: .
    ports:
      - "5000:5000"  # 前端端口
      - "3000:3000"  # 后端 API 端口
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - PORT=3000
    restart: unless-stopped
    container_name: network-debug-helper
    volumes:
      - ./logs:/app/logs  # 日志目录（可选）
    networks:
      - network-debug-net

networks:
  network-debug-net:
    driver: bridge
