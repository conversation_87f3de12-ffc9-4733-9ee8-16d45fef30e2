# ✅ CentOS 部署检查清单

## 📋 部署前检查

### 系统环境
- [ ] CentOS 7/8/9 或 RHEL 系统
- [ ] 至少 1GB 可用内存
- [ ] 至少 2GB 可用磁盘空间
- [ ] 具有 sudo 权限的用户账户

### 网络环境
- [ ] 服务器可以访问互联网（下载依赖）
- [ ] 端口 3000 和 5000 未被占用
- [ ] 防火墙配置允许访问这些端口

### 必要工具
- [ ] curl 或 wget 已安装
- [ ] git 已安装（如果从仓库克隆）

## 🚀 部署步骤检查

### 1. 文件准备
- [ ] 项目文件已上传到服务器
- [ ] 部署脚本有执行权限
  ```bash
  chmod +x deploy-centos.sh
  chmod +x deploy-pm2.sh
  ```

### 2. Node.js 安装
- [ ] Node.js 版本 >= 16.0
  ```bash
  node --version
  ```
- [ ] npm 正常工作
  ```bash
  npm --version
  ```

### 3. 项目构建
- [ ] 依赖安装成功
  ```bash
  npm install
  ```
- [ ] 前端构建成功
  ```bash
  npm run build
  ```
- [ ] dist 目录已生成

### 4. 防火墙配置
- [ ] 端口 3000 已开放
- [ ] 端口 5000 已开放
- [ ] 防火墙规则已保存

### 5. 服务启动
- [ ] 后端服务正常启动
- [ ] 前端服务正常启动
- [ ] 服务状态检查通过

## 🔍 部署验证检查

### 本地访问测试
- [ ] 后端健康检查通过
  ```bash
  curl http://localhost:3000/api/health
  ```
- [ ] 前端页面可访问
  ```bash
  curl http://localhost:5000
  ```

### 网络访问测试
- [ ] 外网可访问前端
  ```bash
  curl http://your-server-ip:5000
  ```
- [ ] 外网可访问后端 API
  ```bash
  curl http://your-server-ip:3000/api/health
  ```

### 功能测试
- [ ] WebSocket 连接正常
- [ ] TCP 客户端模式工作正常
- [ ] TCP 服务器模式工作正常
- [ ] 数据发送接收正常
- [ ] 日志显示正常

## 🛠️ 服务管理检查

### systemd 服务（如果使用）
- [ ] 后端服务已启用
  ```bash
  sudo systemctl is-enabled network-debug-helper-backend
  ```
- [ ] 前端服务已启用
  ```bash
  sudo systemctl is-enabled network-debug-helper-frontend
  ```
- [ ] 服务开机自启动配置正确

### PM2 服务（如果使用）
- [ ] PM2 进程列表正常
  ```bash
  pm2 list
  ```
- [ ] PM2 开机自启动已配置
  ```bash
  pm2 startup
  pm2 save
  ```
- [ ] 日志文件正常生成

## 🔧 可选配置检查

### Nginx 反向代理（如果使用）
- [ ] Nginx 已安装并启动
- [ ] 配置文件语法正确
  ```bash
  sudo nginx -t
  ```
- [ ] 通过域名可以访问
- [ ] SSL 证书配置正确（如果使用 HTTPS）

### 监控和日志
- [ ] 日志文件正常生成
- [ ] 日志轮转配置正确
- [ ] 监控脚本工作正常

## 🚨 故障排除检查

### 常见问题检查
- [ ] 端口冲突已解决
- [ ] 权限问题已解决
- [ ] SELinux 配置正确
- [ ] 防火墙规则正确

### 日志检查
- [ ] 系统日志无错误
  ```bash
  sudo journalctl -u network-debug-helper-backend --no-pager
  ```
- [ ] 应用日志无严重错误
- [ ] Nginx 日志无错误（如果使用）

## 📊 性能检查

### 资源使用
- [ ] CPU 使用率正常
- [ ] 内存使用率正常
- [ ] 磁盘空间充足

### 网络性能
- [ ] 响应时间正常
- [ ] 并发连接处理正常
- [ ] WebSocket 连接稳定

## 🔐 安全检查

### 基础安全
- [ ] 服务以非 root 用户运行
- [ ] 不必要的端口已关闭
- [ ] 系统更新到最新版本

### 网络安全
- [ ] 防火墙规则最小化
- [ ] 只开放必要的端口
- [ ] 考虑使用 VPN 或内网访问

## 📝 部署完成确认

### 最终检查
- [ ] 所有服务正常运行
- [ ] 功能测试全部通过
- [ ] 性能表现符合预期
- [ ] 安全配置已完成

### 文档记录
- [ ] 部署配置已记录
- [ ] 管理员账户信息已保存
- [ ] 备份策略已制定
- [ ] 监控告警已配置

## 🎉 部署成功标志

当以下所有项目都完成时，表示部署成功：

✅ **服务状态**: 所有服务正常运行  
✅ **网络访问**: 前端和后端都可以正常访问  
✅ **功能验证**: TCP 客户端和服务器模式都工作正常  
✅ **日志记录**: 连接状态和数据传输日志正常显示  
✅ **性能表现**: 响应时间和资源使用在合理范围内  

## 📞 获取帮助

如果在部署过程中遇到问题：

1. 检查本清单中的相关项目
2. 查看详细的错误日志
3. 参考 [CentOS 部署指南](./CENTOS_DEPLOYMENT.md)
4. 运行自动检查脚本：`npm run check`
5. 提交 Issue 描述具体问题
