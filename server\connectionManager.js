import { EventEmitter } from 'events';

/**
 * 连接管理器类
 * 管理 WebSocket 连接、TCP 服务器和客户端连接
 */
class ConnectionManager extends EventEmitter {
    constructor() {
        super();
        this.activeConnections = new Map(); // WebSocket 连接
        this.serverSocket = null; // TCP 服务器 socket
        this.clientSockets = new Set(); // TCP 客户端连接集合
        this.serverTask = null; // TCP 服务器任务
        this.isServerRunning = false; // 服务器运行状态
        this.currentWebSocket = null; // 当前 WebSocket 引用
        this.clientSocket = null; // TCP 客户端 socket
        this.clientTask = null; // TCP 客户端任务
    }

    /**
     * 添加 WebSocket 连接
     */
    addConnection(clientId, websocket) {
        this.activeConnections.set(clientId, websocket);
        this.currentWebSocket = websocket;
        console.log(`WebSocket 连接已添加: ${clientId}`);
    }

    /**
     * 移除 WebSocket 连接
     */
    removeConnection(clientId) {
        if (this.activeConnections.has(clientId)) {
            this.activeConnections.delete(clientId);
            if (this.currentWebSocket && this.getWebSocketId(this.currentWebSocket) === clientId) {
                this.currentWebSocket = null;
            }
            console.log(`WebSocket 连接已移除: ${clientId}`);
        }
    }

    /**
     * 获取 WebSocket ID
     */
    getWebSocketId(websocket) {
        for (const [id, ws] of this.activeConnections) {
            if (ws === websocket) {
                return id;
            }
        }
        return null;
    }

    /**
     * 停止 TCP 服务器
     */
    async stopServer() {
        if (this.serverTask) {
            clearInterval(this.serverTask);
            this.serverTask = null;
        }

        if (this.serverSocket) {
            this.serverSocket.close();
            this.serverSocket = null;
        }

        // 关闭所有客户端连接
        for (const clientSocket of this.clientSockets) {
            try {
                clientSocket.destroy();
            } catch (error) {
                console.error('关闭客户端连接时出错:', error);
            }
        }
        this.clientSockets.clear();
        this.isServerRunning = false;
        console.log('TCP 服务器已停止');
    }

    /**
     * 停止 TCP 客户端
     */
    async stopClient() {
        if (this.clientTask) {
            clearInterval(this.clientTask);
            this.clientTask = null;
        }

        if (this.clientSocket) {
            this.clientSocket.destroy();
            this.clientSocket = null;
        }
        console.log('TCP 客户端已停止');
    }

    /**
     * 向 WebSocket 发送消息
     */
    sendToWebSocket(message) {
        if (this.currentWebSocket && this.currentWebSocket.readyState === 1) {
            try {
                this.currentWebSocket.send(JSON.stringify(message));
                return true;
            } catch (error) {
                console.error('WebSocket 发送消息失败:', error);
                return false;
            }
        }
        return false;
    }

    /**
     * 格式化时间戳
     */
    formatTimestamp() {
        const now = new Date();
        return now.getFullYear() + '-' +
            (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
            now.getDate().toString().padStart(2, '0') + ' ' +
            now.getHours().toString().padStart(2, '0') + ':' +
            now.getMinutes().toString().padStart(2, '0') + ':' +
            now.getSeconds().toString().padStart(2, '0') + '.' +
            now.getMilliseconds().toString().padStart(3, '0');
    }

    /**
     * 处理接收到的数据
     */
    handleReceivedData(data, clientInfo) {
        const message = {
            type: 'data',
            direction: 'receive',
            data: Array.from(data).map(byte => byte.toString(16).padStart(2, '0').toUpperCase()).join(' '), // 发送十六进制格式
            rawData: data.toString('latin1'), // 保留原始数据用于 ASCII 显示
            timestamp: this.formatTimestamp(),
            client: clientInfo
        };

        this.sendToWebSocket(message);
    }

    /**
     * 发送错误消息
     */
    sendError(errorMessage) {
        const message = {
            type: 'error',
            message: errorMessage
        };
        this.sendToWebSocket(message);
    }
}

export default ConnectionManager;
