{"name": "network-debug-helper", "version": "0.0.0", "type": "module", "scripts": {"start": "node server/server.js", "check": "node check-deployment.js", "setup:ip": "node scripts/setup-ip.js", "setup:ip:simple": "node scripts/simple-setup.js", "setup:ip:shell": "chmod +x scripts/centos-setup-ip.sh && scripts/centos-setup-ip.sh", "config:show": "cat .env 2>/dev/null || echo \"配置文件不存在，请运行配置脚本\""}, "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5"}}