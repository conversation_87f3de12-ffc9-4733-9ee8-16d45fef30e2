# 网络调试助手 - 部署总结

## 🎯 部署目标

- **前端服务**: 端口 5000 (用户访问入口)
- **后端服务**: 端口 3000 (API 服务)
- **日志管理**: PM2 进程管理，实时日志查看
- **自动化部署**: 一键部署脚本，支持 CentOS/Ubuntu/Debian

## 📦 部署包内容

```
network-debug-helper-deploy.tar.gz (约 400KB)
├── network-debug-helper/
│   ├── public/                 # 前端构建文件
│   │   ├── index.html
│   │   └── assets/
│   ├── server/                 # 后端源码
│   │   ├── server.js          # 主服务器
│   │   ├── static-server.js   # 静态文件服务器
│   │   ├── connectionManager.js
│   │   ├── tcpServer.js
│   │   └── tcpClient.js
│   ├── package.json           # 生产依赖
│   ├── ecosystem.config.js    # PM2 配置
│   ├── deploy.sh             # 自动部署脚本
│   └── README.md             # 说明文档
```

## 🚀 快速部署流程

### 1. 本地构建
```bash
npm run build:prod
```

### 2. 上传到服务器
```bash
scp network-debug-helper-deploy.tar.gz user@server:/path/to/deploy/
```

### 3. 服务器部署
```bash
tar -xzf network-debug-helper-deploy.tar.gz
cd network-debug-helper
chmod +x deploy.sh
./deploy.sh
```

### 4. 访问应用
- 前端: http://server-ip:5000
- 后端: http://server-ip:3000

## 🔧 服务管理

### PM2 进程管理
```bash
pm2 status                    # 查看状态
pm2 logs                      # 查看日志
pm2 restart all              # 重启服务
pm2 stop all                 # 停止服务
```

### 日志文件
- 后端日志: `./logs/combined.log`
- 前端日志: `./logs/frontend-combined.log`
- 错误日志: `./logs/err.log`

### 实时日志查看
```bash
pm2 logs network-debug-helper        # 后端日志
pm2 logs network-debug-helper-frontend # 前端日志
tail -f logs/combined.log            # 文件日志
```

## 🌐 网络配置

### 端口要求
- **5000**: 前端服务 (必须开放)
- **3000**: 后端API (内部通信)

### 防火墙配置
```bash
# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload

# Ubuntu/Debian
sudo ufw allow 5000
sudo ufw allow 3000
```

## 📊 系统要求

### 最低配置
- **CPU**: 1 核心
- **内存**: 512MB
- **磁盘**: 100MB
- **系统**: CentOS 7+, Ubuntu 18.04+, Debian 9+

### 推荐配置
- **CPU**: 2 核心
- **内存**: 1GB
- **磁盘**: 1GB
- **网络**: 稳定的网络连接

## 🔍 健康检查

### 服务状态检查
```bash
# PM2 状态
pm2 status

# 端口监听检查
netstat -tuln | grep :5000
netstat -tuln | grep :3000

# 服务响应检查
curl http://localhost:3000/api/health
curl http://localhost:5000
```

### 预期响应
- **后端健康检查**: `{"status":"ok","serverRunning":false,"clientConnected":false,"activeConnections":0}`
- **前端访问**: 返回 HTML 页面

## 🐛 常见问题

### 1. 端口被占用
```bash
lsof -i :5000
lsof -i :3000
pm2 stop all
```

### 2. Node.js 版本过低
```bash
node --version  # 需要 >= 16.0.0
# 重新安装 Node.js
```

### 3. 权限问题
```bash
chmod +x deploy.sh
sudo ./deploy.sh  # 如果需要
```

### 4. 服务无法启动
```bash
pm2 logs --lines 50
node server/server.js  # 手动测试
```

## 📈 性能监控

### PM2 监控
```bash
pm2 monit                    # 实时监控
pm2 show network-debug-helper # 详细信息
```

### 系统资源
```bash
top                          # CPU/内存使用
df -h                        # 磁盘使用
free -h                      # 内存状态
```

## 🔄 更新部署

### 更新流程
1. 构建新版本部署包
2. 停止当前服务: `pm2 stop all`
3. 备份当前版本
4. 部署新版本
5. 启动服务: `pm2 start ecosystem.config.js`

### 回滚流程
1. 停止服务: `pm2 stop all`
2. 恢复备份版本
3. 启动服务: `pm2 start ecosystem.config.js`

## 📞 技术支持

### 日志分析
1. 查看 PM2 日志: `pm2 logs`
2. 查看文件日志: `tail -f logs/combined.log`
3. 检查错误日志: `tail -f logs/err.log`

### 问题排查步骤
1. 检查服务状态: `pm2 status`
2. 检查端口监听: `netstat -tuln`
3. 检查防火墙: `firewall-cmd --list-ports`
4. 检查系统资源: `top`, `free -h`
5. 查看详细日志: `pm2 logs --lines 100`

## ✅ 部署验证清单

- [ ] 服务器满足系统要求
- [ ] 网络端口已开放 (5000, 3000)
- [ ] 部署包已上传并解压
- [ ] 部署脚本执行成功
- [ ] PM2 服务状态正常
- [ ] 前端页面可正常访问
- [ ] 后端API健康检查通过
- [ ] 日志文件正常生成
- [ ] 开机自启动已设置

## 🎉 部署完成

恭喜！网络调试助手已成功部署到 Linux 服务器。

**访问地址**: http://your-server-ip:5000

现在您可以：
- 使用TCP客户端连接远程服务器
- 启动TCP服务器监听客户端连接
- 实时收发ASCII和HEX格式数据
- 查看美化的连接状态和数据日志
- 通过PM2管理和监控服务运行状态

享受高效的网络调试体验！
