# 网络调试助手部署指南

## 🚀 快速部署到 Linux 服务器

### 📋 部署前准备

#### 系统要求
- **操作系统**: CentOS 7+, Ubuntu 18.04+, Debian 9+
- **内存**: 最小 512MB，推荐 1GB+
- **磁盘**: 最小 100MB 可用空间
- **网络**: 需要开放 3000 和 5000 端口

#### 端口说明
- **5000**: 前端服务端口（用户访问）
- **3000**: 后端API服务端口

### 🔧 自动化部署（推荐）

#### 步骤 1: 构建部署包

在开发机器上执行：
```bash
# 构建并打包
npm run build:prod

# 或者分步执行
npm run build
npm run package
```

执行完成后会生成 `network-debug-helper-deploy.tar.gz` 文件。

#### 步骤 2: 上传到服务器

```bash
# 使用 scp 上传
scp network-debug-helper-deploy.tar.gz user@your-server:/home/<USER>/

# 或使用其他方式上传文件
```

#### 步骤 3: 在服务器上部署

```bash
# 解压部署包
tar -xzf network-debug-helper-deploy.tar.gz
cd network-debug-helper

# 运行自动部署脚本
chmod +x deploy.sh
./deploy.sh
```

部署脚本会自动：
- 检测系统类型
- 安装 Node.js（如果未安装）
- 安装 PM2 进程管理器
- 安装项目依赖
- 检查端口占用
- 启动服务
- 设置开机自启

#### 步骤 4: 访问应用

部署完成后，访问：
- **前端**: http://your-server-ip:5000
- **后端API**: http://your-server-ip:3000/api/health

### 🛠️ 手动部署

如果自动部署脚本遇到问题，可以手动部署：

#### 1. 安装 Node.js

**CentOS/RHEL:**
```bash
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo yum install -y nodejs
```

**Ubuntu/Debian:**
```bash
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. 安装 PM2

```bash
npm install -g pm2
```

#### 3. 安装项目依赖

```bash
cd network-debug-helper
npm install --production
```

#### 4. 创建日志目录

```bash
mkdir -p logs
```

#### 5. 启动服务

```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 📊 服务管理

#### PM2 常用命令

```bash
# 查看服务状态
pm2 status

# 查看实时日志
pm2 logs                              # 所有服务
pm2 logs network-debug-helper        # 后端服务
pm2 logs network-debug-helper-frontend # 前端服务

# 重启服务
pm2 restart network-debug-helper     # 重启后端
pm2 restart all                      # 重启所有

# 停止服务
pm2 stop network-debug-helper        # 停止后端
pm2 stop all                         # 停止所有

# 删除服务
pm2 delete network-debug-helper      # 删除后端
pm2 delete all                       # 删除所有
```

#### 日志文件位置

```
logs/
├── combined.log           # 后端综合日志
├── err.log               # 后端错误日志
├── out.log               # 后端输出日志
├── frontend-combined.log # 前端综合日志
├── frontend-err.log      # 前端错误日志
└── frontend-out.log      # 前端输出日志
```

#### 查看日志

```bash
# 实时查看后端日志
tail -f logs/combined.log

# 实时查看前端日志
tail -f logs/frontend-combined.log

# 查看最近100行日志
tail -n 100 logs/combined.log

# 查看错误日志
tail -f logs/err.log
```

### 🔥 防火墙配置

#### CentOS/RHEL (firewalld)

```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

#### Ubuntu/Debian (ufw)

```bash
# 开放端口
sudo ufw allow 3000
sudo ufw allow 5000

# 查看状态
sudo ufw status
```

### 🐛 故障排除

#### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
lsof -i :3000
lsof -i :5000

# 杀死占用进程
sudo kill -9 <PID>
```

2. **Node.js 版本过低**
```bash
# 检查版本
node --version

# 如果版本低于 16，重新安装
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo yum install -y nodejs
```

3. **权限问题**
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 如果需要 root 权限
sudo ./deploy.sh
```

4. **服务启动失败**
```bash
# 查看详细错误
pm2 logs network-debug-helper --lines 50

# 手动启动测试
node server/server.js
```

5. **无法访问服务**
```bash
# 检查服务状态
pm2 status

# 检查端口监听
netstat -tuln | grep :5000
netstat -tuln | grep :3000

# 检查防火墙
sudo firewall-cmd --list-ports  # CentOS
sudo ufw status                 # Ubuntu
```

### 📈 性能优化

#### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

#### PM2 配置优化

编辑 `ecosystem.config.js`：
```javascript
{
  instances: 'max',        // 使用所有CPU核心
  max_memory_restart: '1G', // 内存限制
  node_args: '--max-old-space-size=1024' // Node.js 内存限制
}
```

### 🔄 更新部署

#### 更新应用

```bash
# 停止服务
pm2 stop all

# 备份当前版本
cp -r network-debug-helper network-debug-helper-backup

# 上传新版本并解压
tar -xzf network-debug-helper-deploy-new.tar.gz

# 启动服务
pm2 start ecosystem.config.js
```

#### 回滚版本

```bash
# 停止服务
pm2 stop all

# 恢复备份
rm -rf network-debug-helper
mv network-debug-helper-backup network-debug-helper

# 启动服务
cd network-debug-helper
pm2 start ecosystem.config.js
```

### 📞 技术支持

如果遇到部署问题，请：
1. 查看日志文件确定具体错误
2. 检查系统要求是否满足
3. 确认网络和防火墙配置
4. 参考故障排除章节

部署成功后，您就可以通过浏览器访问网络调试助手了！
