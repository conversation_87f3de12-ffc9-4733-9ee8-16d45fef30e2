# 🚀 网络调试助手部署指南

本指南将帮助您快速部署网络调试助手到生产环境，通过端口 5000 访问。

## 📋 部署前准备

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器
- 确保端口 3000 和 5000 未被占用

### 检查环境
```bash
node --version  # 应该 >= 16.0
npm --version   # 确认 npm 可用
```

## 🎯 快速部署

### 方式一：一键部署（推荐）

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

### 方式二：手动部署

```bash
# 1. 安装依赖
npm install

# 2. 构建前端
npm run build

# 3. 启动生产服务
npm run deploy
```

### 方式三：分步部署

```bash
# 1. 安装依赖
npm install

# 2. 构建前端
npm run build

# 3. 分别启动服务
npm run server:prod  # 后端服务 (端口 3000)
npm run preview -- --port 5000 --host  # 前端服务 (端口 5000)
```

## 🔍 部署验证

### 自动检查
```bash
npm run check
```

### 手动检查
1. **前端服务**: 访问 http://localhost:5000
2. **后端 API**: 访问 http://localhost:3000/api/health

## 🌐 访问地址

部署成功后，您可以通过以下地址访问：

- **主应用**: http://localhost:5000
- **网络访问**: http://[您的IP]:5000
- **API 服务**: http://localhost:3000

## 🐳 Docker 部署

### 使用 Docker Compose（推荐）
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 使用 Docker 直接部署
```bash
# 构建镜像
docker build -t network-debug-helper .

# 运行容器
docker run -d \
  --name network-debug-helper \
  -p 5000:5000 \
  -p 3000:3000 \
  network-debug-helper

# 查看日志
docker logs -f network-debug-helper
```

## ⚙️ 配置说明

### 端口配置
- **前端端口**: 5000（可在 vite.config.js 中修改）
- **后端端口**: 3000（可通过环境变量 PORT 修改）

### 环境变量
```bash
NODE_ENV=production    # 运行环境
HOST=0.0.0.0          # 服务器监听地址
PORT=3000             # 后端服务端口
```

### 自定义端口
如果需要修改端口，请编辑以下文件：

**前端端口 (vite.config.js):**
```javascript
preview: {
  port: 5000,  // 修改为您需要的端口
  host: true
}
```

**后端端口 (环境变量):**
```bash
PORT=3000 npm run server:prod
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :5000  # Windows
lsof -i :5000                 # Linux/macOS

# 解决方案：修改端口或停止占用进程
```

#### 2. 构建失败
```bash
# 清理缓存重新构建
rm -rf node_modules package-lock.json
npm install
npm run build
```

#### 3. 服务启动失败
```bash
# 检查日志
npm run server:prod  # 查看后端日志
npm run preview      # 查看前端日志
```

#### 4. 网络访问问题
- 确保防火墙允许端口 5000 和 3000
- 检查网络配置是否正确
- 确认服务器监听地址为 0.0.0.0

### 日志查看

**查看实时日志:**
```bash
# 部署日志
npm run deploy

# 检查服务状态
npm run check

# 查看进程
ps aux | grep node  # Linux/macOS
tasklist | findstr node  # Windows
```

## 📊 性能优化

### 生产环境优化
1. **启用 Gzip 压缩**: 已在构建中启用
2. **代码分割**: 已配置 vendor 和 icons 分包
3. **资源压缩**: 使用 Terser 压缩 JavaScript
4. **CSS 优化**: 使用 PostCSS 优化样式

### 监控建议
- 使用 PM2 进行进程管理
- 配置日志轮转
- 设置健康检查
- 监控内存和 CPU 使用率

## 🔄 更新部署

### 更新应用
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖
npm install

# 3. 重新构建和部署
npm run deploy
```

### 零停机更新
```bash
# 使用 PM2 进行零停机更新
pm2 start ecosystem.config.js
pm2 reload all
```

## 🛡️ 安全建议

1. **反向代理**: 使用 Nginx 作为反向代理
2. **HTTPS**: 配置 SSL 证书
3. **防火墙**: 限制不必要的端口访问
4. **更新**: 定期更新依赖包

## 📞 获取帮助

如果遇到部署问题：

1. 查看控制台错误信息
2. 运行 `npm run check` 检查服务状态
3. 查看相关日志文件
4. 提交 Issue 描述问题

## 🎉 部署成功

部署成功后，您将看到：
- ✅ 前端服务运行在 http://localhost:5000
- ✅ 后端 API 运行在 http://localhost:3000
- ✅ 所有功能正常工作

现在您可以开始使用网络调试助手进行 TCP 网络调试了！
