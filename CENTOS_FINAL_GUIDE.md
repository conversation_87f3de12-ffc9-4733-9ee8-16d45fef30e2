# 🐧 CentOS 部署最终指南

## 📦 打包完成

您的网络调试助手已成功打包为 CentOS 部署包：
- **文件名**: `network-debug-helper-0.0.0-centos.tar.gz`
- **大小**: 0.37 MB
- **包含内容**: 前端构建文件、后端源码、安装脚本、服务配置

## 🚀 CentOS 部署步骤

### 步骤 1: 上传部署包

将打包文件上传到您的 CentOS 服务器：

```bash
# 使用 scp 上传
scp network-debug-helper-0.0.0-centos.tar.gz user@your-centos-server:/home/<USER>/

# 或使用其他方式（FTP、SFTP、wget 等）
```

### 步骤 2: 在 CentOS 服务器上解压

```bash
# 解压文件
tar -xzf network-debug-helper-0.0.0-centos.tar.gz

# 进入目录
cd network-debug-helper-0.0.0
```

### 步骤 3: 运行安装脚本

```bash
# 给安装脚本执行权限
chmod +x install.sh

# 运行安装脚本
./install.sh
```

安装脚本会自动：
- 检查 Node.js 环境
- 安装生产依赖
- 配置必要权限

### 步骤 4: 启动应用

```bash
# 启动应用
npm start
```

### 步骤 5: 验证部署

```bash
# 检查服务状态
npm run check

# 或手动验证
curl http://localhost:5000
curl http://localhost:3000/api/health
```

## 🌐 访问应用

部署成功后，您可以通过以下地址访问：

- **前端界面**: http://服务器IP:5000
- **后端 API**: http://服务器IP:3000

## 🔧 生产环境配置

### 使用 systemd 服务（推荐）

```bash
# 1. 复制到系统目录
sudo cp -r . /opt/network-debug-helper
sudo chown -R nodejs:nodejs /opt/network-debug-helper

# 2. 安装 systemd 服务
sudo cp network-debug-helper.service /etc/systemd/system/
sudo systemctl daemon-reload

# 3. 启动并启用服务
sudo systemctl enable network-debug-helper
sudo systemctl start network-debug-helper

# 4. 检查状态
sudo systemctl status network-debug-helper
```

### 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

## 📋 环境要求检查清单

在部署前，请确保您的 CentOS 系统满足以下要求：

### ✅ 系统要求
- [ ] CentOS 7/8/9 或 RHEL 7/8/9
- [ ] 至少 512MB 内存
- [ ] 至少 1GB 可用磁盘空间
- [ ] 网络连接正常

### ✅ 软件要求
- [ ] Node.js 16.0 或更高版本
- [ ] npm 包管理器
- [ ] 端口 3000 和 5000 可用

### ✅ 网络要求
- [ ] 端口 3000 和 5000 未被占用
- [ ] 防火墙允许相应端口
- [ ] 如果有 SELinux，需要正确配置

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. Node.js 未安装或版本过低

```bash
# 安装 Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证版本
node -v
npm -v
```

#### 2. 端口被占用

```bash
# 查看端口占用
sudo netstat -tulnp | grep :5000
sudo netstat -tulnp | grep :3000

# 停止占用进程
sudo kill -9 <PID>
```

#### 3. 防火墙阻止访问

```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 开放端口
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

#### 4. 权限问题

```bash
# 检查文件权限
ls -la /opt/network-debug-helper

# 修复权限
sudo chown -R nodejs:nodejs /opt/network-debug-helper
```

### 查看日志

```bash
# 应用日志
sudo journalctl -u network-debug-helper -f

# 系统日志
sudo tail -f /var/log/messages
```

## 🔄 更新应用

当有新版本时，按以下步骤更新：

```bash
# 1. 停止服务
sudo systemctl stop network-debug-helper

# 2. 备份当前版本
sudo cp -r /opt/network-debug-helper /opt/network-debug-helper.backup

# 3. 上传新版本并解压
tar -xzf network-debug-helper-新版本-centos.tar.gz

# 4. 复制新文件
sudo cp -r network-debug-helper-新版本/* /opt/network-debug-helper/

# 5. 安装依赖
cd /opt/network-debug-helper
sudo -u nodejs npm install --production

# 6. 重启服务
sudo systemctl start network-debug-helper
```

## 📞 技术支持

如果在部署过程中遇到问题：

1. **检查系统要求**: 确保满足所有环境要求
2. **查看日志**: 检查应用和系统日志
3. **网络测试**: 验证端口和网络连接
4. **权限检查**: 确保文件和目录权限正确

## 🎉 部署成功

部署成功后，您将拥有：

- ✅ 功能完整的网络调试助手
- ✅ 美化的数据日志显示
- ✅ TCP 客户端和服务器模式
- ✅ 实时连接状态监控
- ✅ ASCII 和 HEX 数据格式支持
- ✅ 系统服务自动启动

现在您可以开始使用网络调试助手进行 TCP 网络调试了！

**访问地址**: http://您的服务器IP:5000
