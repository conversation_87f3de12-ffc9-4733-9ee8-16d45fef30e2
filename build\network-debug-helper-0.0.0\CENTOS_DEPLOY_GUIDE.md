# 🐧 CentOS 部署完整指南

本指南将详细介绍如何将网络调试助手部署到 CentOS 系统上。

## 📋 系统要求

### 最低要求
- **操作系统**: CentOS 7/8/9 或 RHEL 7/8/9
- **内存**: 512MB RAM (推荐 1GB+)
- **磁盘**: 1GB 可用空间
- **网络**: 能够访问互联网下载依赖

### 端口要求
- **3000**: 后端 API 服务
- **5000**: 前端 Web 服务

## 🚀 快速部署

### 步骤 1: 打包应用

在开发机器上执行：

```bash
# 创建 CentOS 部署包
npm run package:centos
```

这将生成一个 `network-debug-helper-x.x.x-centos.tar.gz` 文件。

### 步骤 2: 上传到 CentOS 服务器

```bash
# 使用 scp 上传
scp network-debug-helper-*-centos.tar.gz user@your-server:/home/<USER>/

# 或使用其他方式上传文件
```

### 步骤 3: 在 CentOS 上部署

```bash
# 解压文件
tar -xzf network-debug-helper-*-centos.tar.gz
cd network-debug-helper-*

# 运行安装脚本
chmod +x install.sh
./install.sh

# 启动应用
npm start
```

### 步骤 4: 验证部署

```bash
# 检查服务状态
npm run check

# 或手动检查
curl http://localhost:5000
curl http://localhost:3000/api/health
```

## 🔧 详细部署步骤

### 1. 环境准备

#### 自动安装环境 (推荐)

如果您的 CentOS 系统还没有安装 Node.js，可以使用我们提供的自动安装脚本：

```bash
# 下载环境检查脚本
wget https://raw.githubusercontent.com/your-repo/scripts/centos-check.sh
chmod +x centos-check.sh
./centos-check.sh

# 如果环境不满足要求，运行安装脚本
wget https://raw.githubusercontent.com/your-repo/scripts/centos-install.sh
chmod +x centos-install.sh
sudo ./centos-install.sh
```

#### 手动安装环境

```bash
# 安装 Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node -v  # 应该显示 v18.x.x
npm -v   # 应该显示 npm 版本

# 安装 PM2 (可选)
sudo npm install -g pm2
```

#### 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

### 2. 应用部署

#### 基础部署

```bash
# 1. 解压应用
tar -xzf network-debug-helper-*-centos.tar.gz
cd network-debug-helper-*

# 2. 安装依赖
npm install --production

# 3. 启动应用
npm start
```

#### 生产环境部署

```bash
# 1. 创建应用用户
sudo useradd -r -s /bin/false nodejs

# 2. 创建应用目录
sudo mkdir -p /opt/network-debug-helper
sudo chown nodejs:nodejs /opt/network-debug-helper

# 3. 复制应用文件
sudo cp -r . /opt/network-debug-helper/
sudo chown -R nodejs:nodejs /opt/network-debug-helper

# 4. 安装依赖
cd /opt/network-debug-helper
sudo -u nodejs npm install --production
```

### 3. 服务管理

#### 使用 systemd

```bash
# 1. 安装服务文件
sudo cp network-debug-helper.service /etc/systemd/system/
sudo systemctl daemon-reload

# 2. 启动服务
sudo systemctl enable network-debug-helper
sudo systemctl start network-debug-helper

# 3. 检查状态
sudo systemctl status network-debug-helper

# 4. 查看日志
sudo journalctl -u network-debug-helper -f
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用

```bash
# 查看端口占用
sudo netstat -tulnp | grep :5000
sudo netstat -tulnp | grep :3000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 2. 权限问题

```bash
# 检查文件权限
ls -la /opt/network-debug-helper

# 修复权限
sudo chown -R nodejs:nodejs /opt/network-debug-helper
```

#### 3. 防火墙问题

```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 检查端口开放
sudo firewall-cmd --list-all
```

### 日志查看

```bash
# 应用日志
sudo journalctl -u network-debug-helper -f

# 系统日志
sudo tail -f /var/log/messages
```

## 📞 获取帮助

如果遇到部署问题：

1. 查看应用日志
2. 检查系统资源使用情况
3. 验证网络连接
4. 参考故障排除章节
5. 提交 Issue 描述问题

部署成功后，您可以通过 `http://服务器IP:5000` 访问网络调试助手！
