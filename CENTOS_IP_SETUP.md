# 🐧 CentOS IP 配置快速指南

## 🚨 解决 "Missing script: setup:ip" 错误

如果您在 CentOS 服务器上遇到 `npm ERR! Missing script: "setup:ip"` 错误，这是因为您使用的是旧版本的部署包。

## 🔧 解决方案

### 方案一：使用 Shell 配置脚本（推荐）

```bash
# 给脚本执行权限
chmod +x scripts/centos-setup-ip.sh

# 运行配置脚本
./scripts/centos-setup-ip.sh
```

### 方案二：手动创建 .env 文件

```bash
# 1. 复制示例配置
cp .env.example .env

# 2. 编辑配置文件
vi .env

# 3. 修改以下配置项
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
API_BASE_URL=http://YOUR_SERVER_IP:3000
WS_URL=ws://YOUR_SERVER_IP:3000/ws
```

### 方案三：重新打包部署

如果您有开发环境访问权限：

```bash
# 在开发环境重新打包
npm run package:centos

# 重新上传并部署新的包
```

## 🎯 快速配置示例

### 本地访问配置
```bash
# .env 文件内容
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
API_BASE_URL=http://localhost:3000
WS_URL=ws://localhost:3000/ws
NODE_ENV=production
```

### 局域网访问配置
```bash
# .env 文件内容（假设服务器IP为 *************）
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
API_BASE_URL=http://*************:3000
WS_URL=ws://*************:3000/ws
NODE_ENV=production
```

## 🚀 应用配置

配置完成后：

```bash
# 重启服务
npm start

# 验证配置
npm run check

# 查看当前配置（如果支持）
npm run config:show 2>/dev/null || cat .env
```

## 🔍 验证部署

```bash
# 检查后端API
curl http://YOUR_SERVER_IP:3000/api/health

# 检查前端访问
# 在浏览器中访问: http://YOUR_SERVER_IP:5000
```

## 📋 可用脚本

在新版本的部署包中，您可以使用以下脚本：

```bash
npm start                # 启动应用
npm run check           # 检查部署状态
npm run setup:ip        # Node.js 配置向导（新版本）
npm run setup:ip:shell  # Shell 配置向导（新版本）
npm run config:show     # 显示当前配置（新版本）
```

## 🛠️ 故障排除

### 1. 脚本不存在
如果 `scripts/centos-setup-ip.sh` 不存在，请手动创建 `.env` 文件。

### 2. 权限问题
```bash
# 给脚本执行权限
chmod +x scripts/centos-setup-ip.sh
```

### 3. 配置不生效
```bash
# 确保 .env 文件在正确位置
ls -la .env

# 重启服务应用配置
npm start
```

### 4. 端口被占用
```bash
# 检查端口占用
netstat -tulnp | grep :3000
netstat -tulnp | grep :5000

# 修改端口配置
vi .env
```

## 💡 最佳实践

1. **使用 Shell 脚本**: 在 CentOS 环境下推荐使用 `centos-setup-ip.sh`
2. **备份配置**: 配置前备份现有的 `.env` 文件
3. **验证配置**: 配置后及时验证服务是否正常
4. **文档记录**: 记录实际使用的IP和端口配置

## 📞 获取帮助

如果仍然遇到问题：

1. 检查 `package.json` 中的 scripts 部分
2. 确认所有必要文件都已上传
3. 查看详细的配置指南：`IP_CONFIG_GUIDE.md`
4. 手动创建配置文件作为备选方案
