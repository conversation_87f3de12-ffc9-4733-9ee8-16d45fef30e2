#!/usr/bin/env node

/**
 * 简化版 IP 配置脚本
 * 兼容性更好的版本
 */

const fs = require('fs');
const readline = require('readline');

// 创建readline接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 提问函数
function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

// 验证IP地址格式
function isValidIP(ip) {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip) || ip === 'localhost' || ip === '0.0.0.0';
}

// 验证端口号
function isValidPort(port) {
    const portNum = parseInt(port);
    return !isNaN(portNum) && portNum > 0 && portNum <= 65535;
}

// 创建.env文件
function createEnvFile(config) {
    const envContent = `# 网络调试助手配置
# 由 simple-setup.js 脚本生成于 ${new Date().toLocaleString()}

# ================================
# 后端服务器配置
# ================================
SERVER_HOST=${config.serverHost}
SERVER_PORT=${config.serverPort}

# 生产环境后端配置
PROD_SERVER_HOST=${config.serverHost}
PROD_SERVER_PORT=${config.serverPort}

# ================================
# 前端配置
# ================================
CLIENT_DEV_PORT=5174
CLIENT_PROD_PORT=${config.frontendPort}
CLIENT_HOST=true

# ================================
# API 连接配置
# ================================
API_BASE_URL=http://${config.apiHost}:${config.serverPort}
WS_URL=ws://${config.apiHost}:${config.serverPort}/ws

# 生产环境 API 配置
PROD_API_BASE_URL=http://${config.apiHost}:${config.serverPort}
PROD_WS_URL=ws://${config.apiHost}:${config.serverPort}/ws

# ================================
# 运行环境
# ================================
NODE_ENV=${config.environment}
`;

    fs.writeFileSync('.env', envContent);
    console.log('✅ .env 文件已创建');
}

// 显示配置预览
function showConfigPreview(config) {
    console.log('\n📋 配置预览:');
    console.log('─'.repeat(50));
    console.log(`后端监听地址: ${config.serverHost}:${config.serverPort}`);
    console.log(`前端访问地址: http://${config.frontendHost}:${config.frontendPort}`);
    console.log(`API 连接地址: http://${config.apiHost}:${config.serverPort}`);
    console.log(`WebSocket 地址: ws://${config.apiHost}:${config.serverPort}/ws`);
    console.log(`运行环境: ${config.environment}`);
    console.log('─'.repeat(50));
}

// 主配置流程
async function setupConfiguration() {
    console.log('🚀 网络调试助手 IP 配置向导 (简化版)');
    console.log('');

    try {
        // 选择配置模式
        console.log('📋 请选择配置模式:');
        console.log('  1. 本地开发 (localhost)');
        console.log('  2. 局域网访问 (需要手动输入IP)');
        console.log('  3. 自定义配置');
        console.log('');

        const mode = await question('请选择模式 (1-3): ');
        let config = {};

        switch (mode) {
            case '1':
                // 本地开发模式
                config = {
                    serverHost: '0.0.0.0',
                    serverPort: '3000',
                    frontendHost: 'localhost',
                    frontendPort: '5000',
                    apiHost: 'localhost',
                    environment: 'development'
                };
                break;

            case '2':
                // 局域网访问模式
                console.log('🔧 局域网访问模式');
                
                let serverIP;
                do {
                    serverIP = await question('请输入服务器IP地址: ');
                    if (!isValidIP(serverIP)) {
                        console.log('❌ 无效的IP地址格式');
                    }
                } while (!isValidIP(serverIP));

                config = {
                    serverHost: '0.0.0.0',
                    serverPort: '3000',
                    frontendHost: serverIP,
                    frontendPort: '5000',
                    apiHost: serverIP,
                    environment: 'production'
                };
                break;

            case '3':
                // 自定义配置模式
                console.log('🔧 自定义配置模式');
                
                // 后端监听地址
                let serverHost;
                do {
                    serverHost = await question('后端监听地址 (0.0.0.0): ') || '0.0.0.0';
                    if (!isValidIP(serverHost)) {
                        console.log('❌ 无效的IP地址格式');
                    }
                } while (!isValidIP(serverHost));

                // 后端端口
                let serverPort;
                do {
                    serverPort = await question('后端端口 (3000): ') || '3000';
                    if (!isValidPort(serverPort)) {
                        console.log('❌ 无效的端口号 (1-65535)');
                    }
                } while (!isValidPort(serverPort));

                // 前端连接IP
                let apiHost;
                do {
                    apiHost = await question('前端连接IP (localhost): ') || 'localhost';
                    if (!isValidIP(apiHost)) {
                        console.log('❌ 无效的IP地址格式');
                    }
                } while (!isValidIP(apiHost));

                // 前端端口
                let frontendPort;
                do {
                    frontendPort = await question('前端端口 (5000): ') || '5000';
                    if (!isValidPort(frontendPort)) {
                        console.log('❌ 无效的端口号 (1-65535)');
                    }
                } while (!isValidPort(frontendPort));

                // 运行环境
                const env = await question('运行环境 (development/production) [production]: ') || 'production';

                config = {
                    serverHost,
                    serverPort,
                    frontendHost: apiHost,
                    frontendPort,
                    apiHost,
                    environment: env
                };
                break;

            default:
                console.log('❌ 无效选择');
                process.exit(1);
        }

        // 显示配置预览
        showConfigPreview(config);

        // 确认配置
        const confirm = await question('\n是否应用此配置? (y/N): ');
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log('❌ 配置已取消');
            process.exit(0);
        }

        // 创建配置文件
        createEnvFile(config);

        // 显示后续步骤
        console.log('\n🎉 配置完成！');
        console.log('\n📋 后续步骤:');
        console.log('1. 重启服务以应用新配置:');
        console.log('   npm start');
        console.log('');
        console.log('2. 访问地址:');
        console.log(`   前端: http://${config.frontendHost}:${config.frontendPort}`);
        console.log(`   API:  http://${config.apiHost}:${config.serverPort}`);
        console.log('');
        console.log('3. 验证配置:');
        console.log(`   curl http://${config.apiHost}:${config.serverPort}/api/health`);

    } catch (error) {
        console.log(`❌ 配置过程中发生错误: ${error.message}`);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 运行配置向导
setupConfiguration();
