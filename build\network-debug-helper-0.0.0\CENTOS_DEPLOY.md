# CentOS 部署指南

## 快速部署

### 1. 解压文件
```bash
tar -xzf network-debug-helper-0.0.0-centos.tar.gz
cd network-debug-helper-0.0.0
```

### 2. 运行安装脚本
```bash
chmod +x install.sh
./install.sh
```

### 3. 启动应用
```bash
npm start
```

### 4. 验证部署
```bash
npm run check
```

## 访问地址

- 前端界面: http://localhost:5000
- 后端 API: http://localhost:3000

## 生产环境部署

### 使用 systemd 服务

1. 复制到系统目录：
```bash
sudo cp -r . /opt/network-debug-helper
sudo chown -R nodejs:nodejs /opt/network-debug-helper
```

2. 安装服务：
```bash
sudo cp network-debug-helper.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable network-debug-helper
sudo systemctl start network-debug-helper
```

3. 检查状态：
```bash
sudo systemctl status network-debug-helper
```
