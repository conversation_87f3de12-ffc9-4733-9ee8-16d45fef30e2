# 🐧 CentOS 系统部署指南

本指南详细介绍如何在 CentOS 7/8/9 系统上部署网络调试助手。

## 📋 系统要求

### 最低配置
- **操作系统**: CentOS 7/8/9 或 RHEL 7/8/9
- **内存**: 1GB RAM
- **存储**: 2GB 可用空间
- **网络**: 开放端口 3000 和 5000

### 推荐配置
- **操作系统**: CentOS 8/9
- **内存**: 2GB RAM
- **存储**: 5GB 可用空间
- **CPU**: 2 核心

## 🚀 快速部署

### 方式一：自动化部署（推荐）

```bash
# 1. 上传项目文件到服务器
scp -r network-debug-helper/ user@your-server:/home/<USER>/

# 2. 登录服务器
ssh user@your-server

# 3. 进入项目目录
cd network-debug-helper

# 4. 执行部署脚本
chmod +x deploy-centos.sh
./deploy-centos.sh
```

### 方式二：PM2 部署（生产环境推荐）

```bash
# 1. 执行 PM2 部署脚本
chmod +x deploy-pm2.sh
./deploy-pm2.sh

# 2. 查看服务状态
pm2 list
pm2 logs
```

## 🔧 手动部署步骤

### 1. 安装 Node.js

#### CentOS 8/9 (使用 dnf)
```bash
# 安装 NodeSource 仓库
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -

# 安装 Node.js
sudo dnf install -y nodejs

# 验证安装
node --version
npm --version
```

#### CentOS 7 (使用 yum)
```bash
# 安装 NodeSource 仓库
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -

# 安装 Node.js
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 配置防火墙

#### 使用 firewalld (CentOS 7+)
```bash
# 检查防火墙状态
sudo systemctl status firewalld

# 开放端口
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 验证规则
sudo firewall-cmd --list-ports
```

#### 使用 iptables
```bash
# 开放端口
sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 5000 -j ACCEPT

# 保存规则
sudo service iptables save
```

### 3. 安装项目依赖

```bash
# 进入项目目录
cd network-debug-helper

# 安装依赖
npm install

# 构建前端
npm run build
```

### 4. 配置系统服务

#### 创建 systemd 服务文件

**后端服务** (`/etc/systemd/system/network-debug-helper-backend.service`):
```ini
[Unit]
Description=Network Debug Helper Backend
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/network-debug-helper
Environment=NODE_ENV=production
Environment=HOST=0.0.0.0
Environment=PORT=3000
ExecStart=/usr/bin/node server/server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

**前端服务** (`/etc/systemd/system/network-debug-helper-frontend.service`):
```ini
[Unit]
Description=Network Debug Helper Frontend
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/network-debug-helper
ExecStart=/usr/bin/npm run preview -- --port 5000 --host
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable network-debug-helper-backend
sudo systemctl enable network-debug-helper-frontend

# 启动服务
sudo systemctl start network-debug-helper-backend
sudo systemctl start network-debug-helper-frontend

# 检查状态
sudo systemctl status network-debug-helper-backend
sudo systemctl status network-debug-helper-frontend
```

## 🌐 Nginx 反向代理配置

### 1. 安装 Nginx

```bash
# CentOS 8/9
sudo dnf install -y nginx

# CentOS 7
sudo yum install -y nginx

# 启动 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 配置 Nginx

```bash
# 复制配置文件
sudo cp nginx.conf /etc/nginx/conf.d/network-debug-helper.conf

# 编辑配置文件，替换域名
sudo vi /etc/nginx/conf.d/network-debug-helper.conf

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

### 3. 配置域名（可选）

如果您有域名，可以配置 DNS 解析：
```bash
# 编辑 hosts 文件（临时测试）
echo "your-server-ip your-domain.com" | sudo tee -a /etc/hosts
```

## 🔍 部署验证

### 1. 检查服务状态

```bash
# 检查端口监听
sudo netstat -tlnp | grep -E ':(3000|5000)'

# 检查进程
ps aux | grep node

# 检查服务状态
sudo systemctl status network-debug-helper-backend
sudo systemctl status network-debug-helper-frontend
```

### 2. 测试访问

```bash
# 测试后端 API
curl http://localhost:3000/api/health

# 测试前端
curl http://localhost:5000

# 外网测试
curl http://your-server-ip:5000
```

### 3. 使用检查脚本

```bash
npm run check
```

## 📊 监控和日志

### 1. 查看系统日志

```bash
# 查看后端日志
sudo journalctl -u network-debug-helper-backend -f

# 查看前端日志
sudo journalctl -u network-debug-helper-frontend -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/network-debug-helper.access.log
sudo tail -f /var/log/nginx/network-debug-helper.error.log
```

### 2. PM2 监控（如果使用 PM2）

```bash
# 查看进程列表
pm2 list

# 查看实时日志
pm2 logs

# 查看监控面板
pm2 monit

# 查看详细信息
pm2 show network-debug-helper-backend
```

## 🛠️ 常见问题解决

### 1. 端口被占用

```bash
# 查看端口占用
sudo lsof -i :3000
sudo lsof -i :5000

# 杀死进程
sudo kill -9 <PID>
```

### 2. 权限问题

```bash
# 修改文件权限
chmod +x deploy-centos.sh
chmod +x deploy-pm2.sh

# 修改目录权限
sudo chown -R $USER:$USER /path/to/network-debug-helper
```

### 3. SELinux 问题

```bash
# 检查 SELinux 状态
sestatus

# 临时禁用 SELinux
sudo setenforce 0

# 永久禁用 SELinux
sudo vi /etc/selinux/config
# 设置 SELINUX=disabled
```

### 4. 防火墙问题

```bash
# 检查防火墙状态
sudo systemctl status firewalld

# 查看开放端口
sudo firewall-cmd --list-ports

# 临时关闭防火墙（不推荐）
sudo systemctl stop firewalld
```

## 🔄 更新和维护

### 1. 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新构建
npm install
npm run build

# 重启服务
sudo systemctl restart network-debug-helper-backend
sudo systemctl restart network-debug-helper-frontend

# 或使用 PM2
pm2 reload all
```

### 2. 备份数据

```bash
# 备份项目文件
tar -czf network-debug-helper-backup-$(date +%Y%m%d).tar.gz network-debug-helper/

# 备份配置文件
sudo cp /etc/systemd/system/network-debug-helper-*.service ~/backup/
```

## 🎉 部署完成

部署成功后，您可以通过以下地址访问：

- **前端界面**: http://your-server-ip:5000
- **后端 API**: http://your-server-ip:3000
- **如果配置了 Nginx**: http://your-domain.com

现在您可以开始使用网络调试助手进行 TCP 网络调试了！
