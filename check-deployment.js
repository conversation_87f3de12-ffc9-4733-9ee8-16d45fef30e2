#!/usr/bin/env node

/**
 * 部署状态检查脚本
 * 检查前端和后端服务是否正常运行
 */

import http from 'http';
import https from 'https';

// 配置
const config = {
    frontend: {
        url: 'http://localhost:5000',
        name: '前端服务'
    },
    backend: {
        url: 'http://localhost:3000/api/health',
        name: '后端 API 服务'
    }
};

// 颜色输出
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查服务状态
function checkService(url, name) {
    return new Promise((resolve) => {
        const protocol = url.startsWith('https') ? https : http;

        const req = protocol.get(url, (res) => {
            if (res.statusCode >= 200 && res.statusCode < 300) {
                log(`✅ ${name} - 运行正常 (状态码: ${res.statusCode})`, 'green');
                resolve(true);
            } else {
                log(`❌ ${name} - 状态异常 (状态码: ${res.statusCode})`, 'red');
                resolve(false);
            }
        });

        req.on('error', (error) => {
            log(`❌ ${name} - 连接失败: ${error.message}`, 'red');
            resolve(false);
        });

        req.setTimeout(5000, () => {
            log(`❌ ${name} - 连接超时`, 'red');
            req.destroy();
            resolve(false);
        });
    });
}

// 主检查函数
async function checkDeployment() {
    log('🔍 开始检查部署状态...', 'blue');
    log('', 'reset');

    const results = [];

    // 检查前端
    log('📱 检查前端服务...', 'yellow');
    const frontendOk = await checkService(config.frontend.url, config.frontend.name);
    results.push({ name: config.frontend.name, status: frontendOk });

    log('', 'reset');

    // 检查后端
    log('🔧 检查后端服务...', 'yellow');
    const backendOk = await checkService(config.backend.url, config.backend.name);
    results.push({ name: config.backend.name, status: backendOk });

    log('', 'reset');

    // 输出总结
    log('📋 部署状态总结:', 'blue');
    log('─'.repeat(40), 'blue');

    let allOk = true;
    results.forEach(result => {
        const status = result.status ? '✅ 正常' : '❌ 异常';
        const color = result.status ? 'green' : 'red';
        log(`${result.name}: ${status}`, color);
        if (!result.status) allOk = false;
    });

    log('─'.repeat(40), 'blue');

    if (allOk) {
        log('🎉 所有服务运行正常！', 'green');
        log('📱 前端访问地址: http://localhost:5000', 'green');
        log('🔧 后端 API 地址: http://localhost:3000', 'green');
        process.exit(0);
    } else {
        log('⚠️  部分服务存在问题，请检查日志', 'red');
        process.exit(1);
    }
}

// 运行检查
checkDeployment().catch(error => {
    log(`❌ 检查过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
});
