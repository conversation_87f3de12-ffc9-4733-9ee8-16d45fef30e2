/**
 * 应用配置文件
 * 统一管理前端和后端的IP地址和端口配置
 */

// 获取环境变量或使用默认值
const getEnvVar = (name, defaultValue) => {
  return typeof process !== 'undefined' && process.env ? process.env[name] || defaultValue : defaultValue;
};

// 后端服务器配置
export const SERVER_CONFIG = {
  // 后端服务器监听地址
  HOST: getEnvVar('SERVER_HOST', '0.0.0.0'),
  
  // 后端服务器端口
  PORT: parseInt(getEnvVar('SERVER_PORT', '3000')),
  
  // 生产环境配置
  PRODUCTION: {
    HOST: getEnvVar('PROD_SERVER_HOST', '0.0.0.0'),
    PORT: parseInt(getEnvVar('PROD_SERVER_PORT', '3000'))
  }
};

// 前端配置
export const CLIENT_CONFIG = {
  // 开发环境前端端口
  DEV_PORT: parseInt(getEnvVar('CLIENT_DEV_PORT', '5174')),
  
  // 生产环境前端端口
  PROD_PORT: parseInt(getEnvVar('CLIENT_PROD_PORT', '5000')),
  
  // 前端监听地址
  HOST: getEnvVar('CLIENT_HOST', 'true'), // true 表示监听所有地址
};

// API 连接配置
export const API_CONFIG = {
  // 后端 API 基础地址
  BASE_URL: getEnvVar('API_BASE_URL', 'http://localhost:3000'),
  
  // WebSocket 连接地址
  WS_URL: getEnvVar('WS_URL', 'ws://localhost:3000/ws'),
  
  // 生产环境 API 配置
  PRODUCTION: {
    BASE_URL: getEnvVar('PROD_API_BASE_URL', 'http://localhost:3000'),
    WS_URL: getEnvVar('PROD_WS_URL', 'ws://localhost:3000/ws')
  }
};

// 根据环境获取配置
export const getConfig = () => {
  const isProduction = getEnvVar('NODE_ENV', 'development') === 'production';
  
  return {
    server: isProduction ? SERVER_CONFIG.PRODUCTION : SERVER_CONFIG,
    api: isProduction ? API_CONFIG.PRODUCTION : API_CONFIG,
    client: CLIENT_CONFIG,
    isProduction
  };
};

// 默认导出配置
export default {
  SERVER_CONFIG,
  CLIENT_CONFIG,
  API_CONFIG,
  getConfig
};
