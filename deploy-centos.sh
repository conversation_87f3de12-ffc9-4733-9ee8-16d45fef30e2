#!/bin/bash

# CentOS 系统网络调试助手部署脚本
# 支持 CentOS 7/8/9

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到 root 用户，建议使用普通用户部署"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测 CentOS 版本
detect_centos_version() {
    if [ -f /etc/centos-release ]; then
        CENTOS_VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
        log_info "检测到 CentOS $CENTOS_VERSION"
    elif [ -f /etc/redhat-release ]; then
        CENTOS_VERSION=$(cat /etc/redhat-release | grep -oE '[0-9]+' | head -1)
        log_info "检测到 RedHat 系列系统版本 $CENTOS_VERSION"
    else
        log_error "未检测到 CentOS/RedHat 系统"
        exit 1
    fi
}

# 安装 Node.js
install_nodejs() {
    log_info "检查 Node.js 安装状态..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "已安装 Node.js $NODE_VERSION"
        
        # 检查版本是否满足要求 (>= 16.0)
        NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
            log_warning "Node.js 版本过低，需要升级到 16.0 或更高版本"
            install_nodejs_from_nodesource
        fi
    else
        log_info "未检测到 Node.js，开始安装..."
        install_nodejs_from_nodesource
    fi
}

# 从 NodeSource 安装 Node.js
install_nodejs_from_nodesource() {
    log_info "从 NodeSource 安装 Node.js 18.x..."
    
    # 安装必要的工具
    if [ "$CENTOS_VERSION" -ge 8 ]; then
        sudo dnf install -y curl
    else
        sudo yum install -y curl
    fi
    
    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    
    # 安装 Node.js
    if [ "$CENTOS_VERSION" -ge 8 ]; then
        sudo dnf install -y nodejs
    else
        sudo yum install -y nodejs
    fi
    
    # 验证安装
    if command -v node &> /dev/null; then
        log_success "Node.js 安装成功: $(node --version)"
        log_success "npm 版本: $(npm --version)"
    else
        log_error "Node.js 安装失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    if systemctl is-active --quiet firewalld; then
        log_info "检测到 firewalld，添加端口规则..."
        sudo firewall-cmd --permanent --add-port=3000/tcp
        sudo firewall-cmd --permanent --add-port=5000/tcp
        sudo firewall-cmd --reload
        log_success "防火墙规则添加成功"
    elif systemctl is-active --quiet iptables; then
        log_info "检测到 iptables，添加端口规则..."
        sudo iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
        sudo iptables -A INPUT -p tcp --dport 5000 -j ACCEPT
        sudo service iptables save
        log_success "iptables 规则添加成功"
    else
        log_warning "未检测到防火墙服务，请手动开放端口 3000 和 5000"
    fi
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件，请确保在项目根目录执行此脚本"
        exit 1
    fi
    
    # 设置 npm 镜像源（可选）
    read -p "是否使用淘宝 npm 镜像源？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        npm config set registry https://registry.npmmirror.com
        log_info "已设置淘宝镜像源"
    fi
    
    npm install
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建前端项目..."
    npm run build
    
    if [ -d "dist" ]; then
        log_success "前端构建成功"
    else
        log_error "前端构建失败"
        exit 1
    fi
}

# 创建 systemd 服务
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    # 获取当前用户和项目路径
    CURRENT_USER=$(whoami)
    PROJECT_PATH=$(pwd)
    
    # 创建后端服务
    sudo tee /etc/systemd/system/network-debug-helper-backend.service > /dev/null <<EOF
[Unit]
Description=Network Debug Helper Backend
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$PROJECT_PATH
Environment=NODE_ENV=production
Environment=HOST=0.0.0.0
Environment=PORT=3000
ExecStart=/usr/bin/node server/server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # 创建前端服务
    sudo tee /etc/systemd/system/network-debug-helper-frontend.service > /dev/null <<EOF
[Unit]
Description=Network Debug Helper Frontend
After=network.target

[Service]
Type=simple
User=$CURRENT_USER
WorkingDirectory=$PROJECT_PATH
ExecStart=/usr/bin/npm run preview -- --port 5000 --host
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载 systemd
    sudo systemctl daemon-reload
    
    # 启用服务
    sudo systemctl enable network-debug-helper-backend
    sudo systemctl enable network-debug-helper-frontend
    
    log_success "systemd 服务创建成功"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动后端服务
    sudo systemctl start network-debug-helper-backend
    sleep 2
    
    # 启动前端服务
    sudo systemctl start network-debug-helper-frontend
    sleep 2
    
    # 检查服务状态
    if systemctl is-active --quiet network-debug-helper-backend; then
        log_success "后端服务启动成功"
    else
        log_error "后端服务启动失败"
        sudo systemctl status network-debug-helper-backend
        exit 1
    fi
    
    if systemctl is-active --quiet network-debug-helper-frontend; then
        log_success "前端服务启动成功"
    else
        log_error "前端服务启动失败"
        sudo systemctl status network-debug-helper-frontend
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    SERVER_IP=$(hostname -I | awk '{print $1}')
    
    echo
    log_success "🎉 部署完成！"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo -e "${GREEN}📱 前端访问地址:${NC}"
    echo -e "   本地访问: http://localhost:5000"
    echo -e "   网络访问: http://$SERVER_IP:5000"
    echo
    echo -e "${GREEN}🔧 后端 API 地址:${NC}"
    echo -e "   本地访问: http://localhost:3000"
    echo -e "   网络访问: http://$SERVER_IP:3000"
    echo
    echo -e "${GREEN}🛠️  服务管理命令:${NC}"
    echo -e "   查看状态: sudo systemctl status network-debug-helper-backend"
    echo -e "   查看状态: sudo systemctl status network-debug-helper-frontend"
    echo -e "   重启服务: sudo systemctl restart network-debug-helper-backend"
    echo -e "   重启服务: sudo systemctl restart network-debug-helper-frontend"
    echo -e "   查看日志: sudo journalctl -u network-debug-helper-backend -f"
    echo -e "   查看日志: sudo journalctl -u network-debug-helper-frontend -f"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# 主函数
main() {
    echo "🚀 CentOS 系统网络调试助手部署脚本"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    check_root
    detect_centos_version
    install_nodejs
    configure_firewall
    install_dependencies
    build_project
    create_systemd_service
    start_services
    show_deployment_info
}

# 执行主函数
main "$@"
