# 网络调试助手 (Network Debug Helper)

一个功能强大的网络调试工具，支持 TCP 客户端和服务器模式，提供美化的数据日志显示和实时连接状态监控。

## ✨ 主要功能

- 🔗 **TCP 客户端模式**: 连接到远程 TCP 服务器
- 🖥️ **TCP 服务器模式**: 监听端口，接受客户端连接
- 📊 **美化数据日志**: 颜色区分、图标标识、结构化显示
- 🔄 **实时状态监控**: 连接建立/断开状态实时显示
- 📡 **双向数据传输**: 支持 ASCII 和 HEX 格式
- 🎨 **现代化界面**: 响应式设计，支持移动端
- 💾 **数据导出**: 支持日志保存和导出功能

## 🚀 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 开发环境运行

```bash
# 克隆项目
git clone <repository-url>
cd network-debug-helper

# 安装依赖
npm install

# 开发模式（同时启动前端和后端）
npm run dev:full

# 或者分别启动
npm run dev      # 启动前端 (http://localhost:5174)
npm run server   # 启动后端 (http://localhost:3000)
```

### 生产环境部署

#### 方式一：使用部署脚本（推荐）

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

#### 方式二：手动部署

```bash
# 安装依赖
npm install

# 构建前端
npm run build

# 启动生产服务（端口5000）
npm run deploy
```

#### 方式三：CentOS 系统部署

**自动化部署:**
```bash
chmod +x deploy-centos.sh
./deploy-centos.sh
```

**PM2 部署（生产环境推荐）:**
```bash
chmod +x deploy-pm2.sh
./deploy-pm2.sh
```

#### 方式四：使用 Docker

```bash
# 构建并启动
docker-compose up -d

# 或者使用 Docker 直接构建
docker build -t network-debug-helper .
docker run -p 5000:5000 -p 3000:3000 network-debug-helper
```

## 📖 使用说明

### 访问地址

- **前端界面**: http://localhost:5000
- **后端 API**: http://localhost:3000

### TCP 客户端模式

1. 选择 "TCP Client" 协议类型
2. 输入远程服务器的 IP 地址和端口
3. 点击 "开始连接"
4. 连接成功后可以发送和接收数据

### TCP 服务器模式

1. 选择 "TCP Server" 协议类型
2. 设置监听地址和端口
3. 点击 "开始监听"
4. 等待客户端连接，连接后可以进行数据交互

### 数据格式

- **ASCII 模式**: 发送/接收普通文本数据
- **HEX 模式**: 发送/接收十六进制格式的二进制数据

### 数据日志特性

- 🟢 绿色：连接成功状态
- 🔴 红色：连接断开状态
- 🔵 蓝色：系统信息
- 🟣 紫色：接收数据
- 🟢 翠绿：发送数据

## 🏗️ 项目架构

```
network-debug-helper/
├── src/                    # 前端源码 (Vue 3)
│   ├── components/         # 组件
│   ├── assets/            # 静态资源
│   └── App.vue            # 主应用组件
├── server/                # 后端源码 (Node.js)
│   ├── server.js          # 主服务器文件
│   ├── connectionManager.js  # 连接管理器
│   ├── tcpServer.js       # TCP 服务器处理
│   └── tcpClient.js       # TCP 客户端处理
├── dist/                  # 构建输出目录
├── deploy.sh              # Linux/macOS 部署脚本
├── deploy.bat             # Windows 部署脚本
├── Dockerfile             # Docker 配置
└── docker-compose.yml     # Docker Compose 配置
```

## 🛠️ 技术栈

### 前端
- **Vue 3**: 渐进式 JavaScript 框架
- **Element Plus**: Vue 3 UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Vite**: 现代化构建工具

### 后端
- **Node.js**: JavaScript 运行时
- **Express**: Web 应用框架
- **WebSocket**: 实时双向通信
- **Net**: Node.js 内置 TCP 模块

## 📋 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览构建结果
npm run server       # 启动后端服务器
npm run server:prod  # 启动生产环境后端
npm run dev:full     # 同时启动前后端（开发）
npm run start        # 启动生产环境（前后端）
npm run deploy       # 构建并部署
```

## 🔧 配置说明

### 端口配置

- **开发环境前端**: 5174
- **生产环境前端**: 5000
- **后端 API**: 3000

### 环境变量

```bash
NODE_ENV=production    # 环境模式
HOST=0.0.0.0          # 服务器监听地址
PORT=3000             # 后端服务端口
```

## 📚 相关文档

- [使用指南](./USAGE_GUIDE.md) - 详细的使用说明
- [连接状态显示](./CONNECTION_STATUS_GUIDE.md) - 连接状态功能说明
- [美化日志显示](./BEAUTIFIED_LOG_GUIDE.md) - 日志美化功能说明
- [CentOS 部署指南](./CENTOS_DEPLOYMENT.md) - CentOS 系统部署详细说明
- [部署指南](./DEPLOYMENT_GUIDE.md) - 通用部署指南

## 🤝 参与贡献

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 故障排除

### 常见问题

1. **端口被占用**: 修改 `vite.config.js` 中的端口配置
2. **依赖安装失败**: 尝试删除 `node_modules` 和 `package-lock.json` 后重新安装
3. **构建失败**: 检查 Node.js 版本是否符合要求

### 获取帮助

如果遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接
3. 提交 Issue 描述问题
