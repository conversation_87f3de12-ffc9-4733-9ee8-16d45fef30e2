# 网络调试助手

一个基于 Vue 3 + Node.js 的网络调试工具，支持 TCP 客户端和服务器模式，提供实时数据收发功能。

## 🌟 功能特性

- 🔌 **TCP 客户端/服务器模式** - 支持作为客户端连接或服务器监听
- 📡 **实时数据收发** - WebSocket 实时通信，零延迟数据传输
- 🎨 **美观的用户界面** - 现代化设计，美化的数据日志显示
- 📊 **数据统计功能** - 实时显示收发字节数统计
- 💾 **数据保存功能** - 支持导出数据日志到文件
- 🔄 **多种数据格式** - 支持 ASCII 和 HEX 格式收发
- 🌈 **美化日志显示** - 颜色区分、图标标识、结构化布局
- 🔗 **连接状态监控** - 实时显示连接建立和断开状态
- ⚙️ **灵活配置** - 支持配置文件自定义 IP 和端口

## 🛠️ 技术栈

- **前端**: Vue 3 + TypeScript + Element Plus + Tailwind CSS
- **后端**: Node.js + Express + WebSocket
- **构建工具**: Vite
- **网络**: TCP Socket + WebSocket

## 🚀 快速开始

### 环境要求

- Node.js 16.0 或更高版本
- npm 或 yarn

### 开发模式

```bash
# 1. 克隆项目
git clone <repository-url>
cd network-debug-helper

# 2. 安装依赖
npm install

# 3. 启动开发环境
npm run dev:full
```

访问 http://localhost:5174 开始使用

### 单独启动

```bash
# 只启动前端 (端口 5174)
npm run dev

# 只启动后端 (端口 3000)
npm run server
```

## 📦 生产部署

### 1. 配置文件设置

复制配置示例文件并修改：

```bash
cp config.example.json config.json
```

编辑 `config.json`：

```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 5000
  },
  "api": {
    "baseUrl": "http://YOUR_SERVER_IP:5000"
  },
  "websocket": {
    "url": "ws://YOUR_SERVER_IP:5000"
  }
}
```

### 2. 构建部署包

```bash
npm run build:deploy
```

这将在 `deploy/` 目录生成完整的部署包。

### 3. 部署到 Linux 服务器

```bash
# 1. 上传部署包到服务器
scp -r deploy/ user@server:/path/to/app/

# 2. 在服务器上启动
cd /path/to/app/deploy/
chmod +x start.sh
./start.sh
```

### 4. 访问应用

浏览器访问：`http://服务器IP:5000`

## ⚙️ 配置说明

### 配置文件结构

```json
{
  "server": {
    "host": "0.0.0.0",     // 服务器监听地址
    "port": 5000           // 服务器端口
  },
  "api": {
    "baseUrl": "http://localhost:5000"  // API 基础 URL
  },
  "websocket": {
    "url": "ws://localhost:5000"        // WebSocket URL
  }
}
```

### 部署场景配置

#### 本地部署
```json
{
  "api": { "baseUrl": "http://localhost:5000" },
  "websocket": { "url": "ws://localhost:5000" }
}
```

#### 局域网部署
```json
{
  "api": { "baseUrl": "http://*************:5000" },
  "websocket": { "url": "ws://*************:5000" }
}
```

#### 公网部署
```json
{
  "api": { "baseUrl": "http://your-domain.com:5000" },
  "websocket": { "url": "ws://your-domain.com:5000" }
}
```
