import { execSync } from 'child_process';
import { readFileSync, writeFileSync, copyFileSync, mkdirSync, existsSync, cpSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { platform } from 'os';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

console.log('🚀 开始构建网络调试助手...');

try {
  // 1. 读取配置文件
  console.log('📖 读取配置文件...');
  const config = JSON.parse(readFileSync(join(rootDir, 'config.json'), 'utf-8'));

  // 2. 更新前端API配置
  console.log('⚙️  更新前端配置...');
  const viteConfigPath = join(rootDir, 'vite.config.js');
  let viteConfig = readFileSync(viteConfigPath, 'utf-8');

  // 创建环境变量配置
  const envConfig = `
// 自动生成的环境配置
window.__APP_CONFIG__ = {
  API_BASE_URL: '${config.api.baseUrl}',
  WS_URL: '${config.websocket.url}',
  SERVER_HOST: '${config.server.host}',
  SERVER_PORT: ${config.server.port}
};
`;

  // 3. 构建前端
  console.log('🏗️  构建前端应用...');
  execSync('npm run build', {
    cwd: rootDir,
    stdio: 'inherit'
  });

  // 4. 创建部署目录
  console.log('📁 创建部署目录...');
  const deployDir = join(rootDir, 'deploy');
  if (!existsSync(deployDir)) {
    mkdirSync(deployDir, { recursive: true });
  }

  // 5. 复制必要文件
  console.log('📋 复制文件...');

  // 复制构建后的前端文件
  cpSync(join(rootDir, 'dist'), join(deployDir, 'dist'), { recursive: true });

  // 复制后端文件
  cpSync(join(rootDir, 'server'), join(deployDir, 'server'), { recursive: true });

  // 复制配置文件
  copyFileSync(join(rootDir, 'config.json'), join(deployDir, 'config.json'));

  // 复制package.json（只包含生产依赖）
  const packageJson = JSON.parse(readFileSync(join(rootDir, 'package.json'), 'utf-8'));
  const prodPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    type: "module",
    scripts: {
      start: "node server/server.js"
    },
    dependencies: {
      express: packageJson.dependencies.express,
      cors: packageJson.dependencies.cors,
      ws: packageJson.dependencies.ws
    }
  };

  writeFileSync(
    join(deployDir, 'package.json'),
    JSON.stringify(prodPackageJson, null, 2)
  );

  // 6. 创建启动脚本
  console.log('📝 创建启动脚本...');

  // Linux启动脚本
  const startScript = `#!/bin/bash
# 网络调试助手启动脚本

echo "🚀 启动网络调试助手..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

# 检查配置文件
if [ ! -f "config.json" ]; then
    echo "❌ 错误: 未找到配置文件 config.json"
    exit 1
fi

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install --production
fi

# 启动服务
echo "🌟 启动服务器..."
node server/server.js
`;

  writeFileSync(join(deployDir, 'start.sh'), startScript);

  // 在非Windows系统上设置执行权限
  if (platform() !== 'win32') {
    try {
      execSync(`chmod +x "${join(deployDir, 'start.sh')}"`);
    } catch (error) {
      console.warn('⚠️  无法设置执行权限，请手动执行: chmod +x start.sh');
    }
  }

  // Windows启动脚本
  const startBat = `@echo off
echo 🚀 启动网络调试助手...

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.json" (
    echo ❌ 错误: 未找到配置文件 config.json
    pause
    exit /b 1
)

REM 安装依赖
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install --production
)

REM 启动服务
echo 🌟 启动服务器...
node server/server.js
pause
`;

  writeFileSync(join(deployDir, 'start.bat'), startBat);

  // 7. 创建README
  const deployReadme = `# 网络调试助手 - 部署包

## 快速启动

### Linux/macOS
\`\`\`bash
chmod +x start.sh
./start.sh
\`\`\`

### Windows
\`\`\`cmd
start.bat
\`\`\`

## 手动启动
\`\`\`bash
npm install --production
node server/server.js
\`\`\`

## 配置说明

编辑 \`config.json\` 文件来修改服务器配置：

\`\`\`json
{
  "server": {
    "host": "0.0.0.0",
    "port": 5000
  }
}
\`\`\`

## 访问地址

服务启动后，在浏览器中访问：
- http://localhost:5000 (本地访问)
- http://服务器IP:5000 (远程访问)

## 系统要求

- Node.js 16.0 或更高版本
- 支持的操作系统：Linux、macOS、Windows
`;

  writeFileSync(join(deployDir, 'README.md'), deployReadme);

  console.log('✅ 构建完成！');
  console.log(`📦 部署包位置: ${deployDir}`);
  console.log('🎯 部署步骤:');
  console.log('   1. 将 deploy 目录上传到服务器');
  console.log('   2. 修改 config.json 中的配置');
  console.log('   3. 运行 ./start.sh (Linux) 或 start.bat (Windows)');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
