import net from 'net';

/**
 * TCP 客户端处理类
 */
class TCPClient {
    constructor(connectionManager) {
        this.connectionManager = connectionManager;
    }

    /**
     * 连接到 TCP 服务器
     */
    async connect(host, port) {
        return new Promise((resolve, reject) => {
            try {
                const socket = new net.Socket();
                
                // 设置连接超时
                socket.setTimeout(10000);

                socket.connect(port, host, () => {
                    console.log(`TCP 客户端已连接到 ${host}:${port}`);
                    this.connectionManager.clientSocket = socket;
                    
                    // 清除超时
                    socket.setTimeout(0);
                    resolve();
                });

                // 处理数据接收
                socket.on('data', (data) => {
                    console.log(`从服务器 ${host}:${port} 接收到数据:`, data.toString());
                    this.connectionManager.handleReceivedData(data, `${host}:${port}`);
                });

                // 处理连接关闭
                socket.on('close', () => {
                    console.log(`与服务器 ${host}:${port} 的连接已关闭`);
                    this.connectionManager.clientSocket = null;
                });

                // 处理连接错误
                socket.on('error', (error) => {
                    console.error(`TCP 客户端连接错误:`, error);
                    this.connectionManager.clientSocket = null;
                    
                    let errorMessage = '客户端连接失败';
                    if (error.code === 'ECONNREFUSED') {
                        errorMessage = `无法连接到 ${host}:${port}，连接被拒绝`;
                    } else if (error.code === 'EHOSTUNREACH') {
                        errorMessage = `无法连接到 ${host}:${port}，主机不可达`;
                    } else if (error.code === 'ETIMEDOUT') {
                        errorMessage = `连接到 ${host}:${port} 超时`;
                    } else {
                        errorMessage = `客户端错误: ${error.message}`;
                    }
                    
                    this.connectionManager.sendError(errorMessage);
                    reject(new Error(errorMessage));
                });

                // 处理超时
                socket.on('timeout', () => {
                    console.error('TCP 客户端连接超时');
                    socket.destroy();
                    const errorMessage = `连接到 ${host}:${port} 超时`;
                    this.connectionManager.sendError(errorMessage);
                    reject(new Error(errorMessage));
                });

            } catch (error) {
                console.error('创建 TCP 客户端时出错:', error);
                reject(error);
            }
        });
    }

    /**
     * 向服务器发送数据
     */
    sendToServer(data) {
        if (!this.connectionManager.clientSocket || this.connectionManager.clientSocket.destroyed) {
            return false;
        }

        try {
            const buffer = Buffer.from(data, 'utf-8');
            this.connectionManager.clientSocket.write(buffer);
            return true;
        } catch (error) {
            console.error('向服务器发送数据失败:', error);
            this.connectionManager.clientSocket = null;
            return false;
        }
    }
}

export default TCPClient;
