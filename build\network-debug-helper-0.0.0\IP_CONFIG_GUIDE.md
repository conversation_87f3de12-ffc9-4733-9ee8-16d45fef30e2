# 🌐 IP 地址配置指南

本指南将详细说明如何修改网络调试助手的前端和后端访问IP地址配置。

## 📋 配置文件位置

### 1. 环境变量配置文件
- **`.env`** - 主要配置文件（需要从 `.env.example` 复制）
- **`.env.example`** - 配置示例文件

### 2. 应用配置文件
- **`config/app.config.js`** - 后端配置管理
- **`src/config/api.config.js`** - 前端API配置

### 3. 服务器配置文件
- **`server/server.js`** - 后端服务器启动配置
- **`vite.config.js`** - 前端开发服务器配置

## 🔧 配置方法

### 方法一：使用环境变量（推荐）

#### 1. 创建 .env 文件
```bash
# 复制示例配置文件
cp .env.example .env
```

#### 2. 编辑 .env 文件
```bash
# 后端服务器配置
SERVER_HOST=0.0.0.0          # 后端监听地址
SERVER_PORT=3000             # 后端端口

# 前端API连接配置
API_BASE_URL=http://*************:3000    # 修改为实际IP
WS_URL=ws://*************:3000/ws         # 修改为实际IP

# 生产环境配置
PROD_API_BASE_URL=http://*************:3000
PROD_WS_URL=ws://*************:3000/ws
```

### 方法二：直接修改配置文件

#### 1. 修改后端配置
编辑 `config/app.config.js`：
```javascript
export const SERVER_CONFIG = {
  HOST: '*************',  // 修改为您的IP
  PORT: 3000,
  // ...
};

export const API_CONFIG = {
  BASE_URL: 'http://*************:3000',  // 修改为您的IP
  WS_URL: 'ws://*************:3000/ws',   // 修改为您的IP
  // ...
};
```

#### 2. 修改前端配置
编辑 `src/config/api.config.js`：
```javascript
const DEFAULT_CONFIG = {
  development: {
    API_BASE_URL: 'http://*************:3000',  // 修改为您的IP
    WS_URL: 'ws://*************:3000/ws'        // 修改为您的IP
  },
  production: {
    API_BASE_URL: 'http://*************:3000',  // 修改为您的IP
    WS_URL: 'ws://*************:3000/ws'        // 修改为您的IP
  }
};
```

## 🎯 常见配置场景

### 场景1: 本地开发（默认）
```bash
# .env 文件
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
API_BASE_URL=http://localhost:3000
WS_URL=ws://localhost:3000/ws
```

### 场景2: 局域网访问
```bash
# .env 文件
SERVER_HOST=0.0.0.0
SERVER_PORT=3000
API_BASE_URL=http://*************:3000
WS_URL=ws://*************:3000/ws
```

### 场景3: 指定网卡IP
```bash
# .env 文件
SERVER_HOST=*************
SERVER_PORT=3000
API_BASE_URL=http://*************:3000
WS_URL=ws://*************:3000/ws
```

### 场景4: 不同端口
```bash
# .env 文件
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
CLIENT_PROD_PORT=8000
API_BASE_URL=http://*************:8080
WS_URL=ws://*************:8080/ws
```

### 场景5: 生产环境部署
```bash
# .env 文件
NODE_ENV=production
PROD_SERVER_HOST=0.0.0.0
PROD_SERVER_PORT=3000
PROD_API_BASE_URL=http://your-server-ip:3000
PROD_WS_URL=ws://your-server-ip:3000/ws
```

## 🔍 配置说明

### 后端配置参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `SERVER_HOST` | 后端监听地址 | `0.0.0.0` | `*************` |
| `SERVER_PORT` | 后端监听端口 | `3000` | `8080` |
| `PROD_SERVER_HOST` | 生产环境监听地址 | `0.0.0.0` | `0.0.0.0` |
| `PROD_SERVER_PORT` | 生产环境监听端口 | `3000` | `3000` |

### 前端配置参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `CLIENT_DEV_PORT` | 开发环境前端端口 | `5174` | `8080` |
| `CLIENT_PROD_PORT` | 生产环境前端端口 | `5000` | `8000` |
| `API_BASE_URL` | API 基础地址 | `http://localhost:3000` | `http://*************:3000` |
| `WS_URL` | WebSocket 地址 | `ws://localhost:3000/ws` | `ws://*************:3000/ws` |

### 监听地址说明

| 地址 | 说明 | 适用场景 |
|------|------|----------|
| `0.0.0.0` | 监听所有网络接口 | 局域网访问、生产环境 |
| `127.0.0.1` | 只监听本地回环 | 本地开发、安全限制 |
| `*************` | 监听指定IP | 指定网卡、多网卡环境 |

## 🚀 应用配置

### 1. 修改配置后重启服务

#### 开发环境
```bash
# 停止当前服务 (Ctrl+C)
# 重新启动
npm run dev:full
```

#### 生产环境
```bash
# 停止服务
sudo systemctl stop network-debug-helper

# 重新启动
sudo systemctl start network-debug-helper
```

### 2. 验证配置

#### 检查后端
```bash
# 检查服务状态
curl http://your-ip:3000/api/health

# 检查 WebSocket
# 在浏览器控制台测试
const ws = new WebSocket('ws://your-ip:3000/ws');
```

#### 检查前端
```bash
# 访问前端
http://your-ip:5000
```

## 🔧 故障排除

### 1. 连接失败
- 检查IP地址是否正确
- 确认端口未被占用
- 验证防火墙设置

### 2. 跨域问题
- 确保后端CORS配置正确
- 检查API地址配置

### 3. WebSocket连接失败
- 验证WebSocket地址配置
- 检查网络连接

### 4. 配置不生效
- 确认 .env 文件位置正确
- 重启服务应用新配置
- 检查环境变量优先级

## 📝 配置检查清单

- [ ] 创建并配置 `.env` 文件
- [ ] 设置正确的 `SERVER_HOST` 和 `SERVER_PORT`
- [ ] 配置正确的 `API_BASE_URL` 和 `WS_URL`
- [ ] 重启前端和后端服务
- [ ] 测试API连接：`curl http://your-ip:3000/api/health`
- [ ] 测试前端访问：`http://your-ip:5000`
- [ ] 验证WebSocket连接正常
- [ ] 测试TCP功能正常工作

## 💡 最佳实践

1. **使用环境变量**: 优先使用 `.env` 文件配置
2. **分环境配置**: 开发和生产环境使用不同配置
3. **安全考虑**: 生产环境避免使用 `0.0.0.0` 如果不需要
4. **文档记录**: 记录实际使用的IP和端口配置
5. **测试验证**: 配置修改后及时测试验证

通过以上配置，您可以灵活地修改网络调试助手的访问IP地址，适应不同的部署环境和网络需求。
