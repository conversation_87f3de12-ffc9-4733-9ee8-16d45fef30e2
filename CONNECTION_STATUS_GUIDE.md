# 连接状态显示功能说明

## 🔗 功能概述

网络调试助手现在会在数据日志中自动显示TCP连接的建立和断开状态，包括IP地址和端口信息。

## 📊 显示内容

### TCP Client 模式

#### 连接建立
```
2024-01-10 10:15:23.456 ℹ️ [系统] 🟢 TCP 客户端已连接到 *************:8070
```

#### 连接断开
```
2024-01-10 10:16:30.789 ℹ️ [系统] 🔴 TCP 客户端已断开连接 *************:8070
```

### TCP Server 模式

#### 服务器启动
```
2024-01-10 10:15:23.456 ℹ️ [系统] 🟢 TCP 服务器已启动，监听 *************:8070
```

#### 客户端连接到服务器
```
2024-01-10 10:15:25.123 ℹ️ [系统] 🟢 新客户端连接: *************:12345
```

#### 客户端断开连接
```
2024-01-10 10:16:30.789 ℹ️ [系统] 🔴 客户端断开连接: *************:12345
```

#### 服务器停止
```
2024-01-10 10:17:00.123 ℹ️ [系统] 🔴 TCP 服务器已停止监听 *************:8070
```

## 🎯 状态图标说明

- 🟢 **绿色圆点**: 连接建立/服务器启动
- 🔴 **红色圆点**: 连接断开/服务器停止
- ℹ️ **信息图标**: 系统状态消息

## 📋 完整示例

### TCP Client 会话示例
```
2024-01-10 10:15:23.456 ℹ️ [系统] 🟢 TCP 客户端已连接到 *************:8070
2024-01-10 10:15:24.789 ↓ [HEX] [16 bytes] 48 5A 00 00 00 00 00 01 01 00 00 00 00 02 CC C1
2024-01-10 10:15:25.123 ↑ [HEX] [4 bytes] 01 02 03 04
2024-01-10 10:15:26.456 ↓ [HEX] [8 bytes] FF FF FF FF 00 00 00 00
2024-01-10 10:16:30.789 ℹ️ [系统] 🔴 TCP 客户端已断开连接 *************:8070
```

### TCP Server 会话示例
```
2024-01-10 10:15:23.456 ℹ️ [系统] 🟢 TCP 服务器已启动，监听 0.0.0.0:8080
2024-01-10 10:15:25.123 ℹ️ [系统] 🟢 新客户端连接: *************:54321
2024-01-10 10:15:26.789 ↓ [ASCII] [12 bytes] Hello Server
2024-01-10 10:15:27.123 ↑ [ASCII] [8 bytes] Hi Client
2024-01-10 10:15:30.456 ℹ️ [系统] 🔴 客户端断开连接: *************:54321
2024-01-10 10:16:00.789 ℹ️ [系统] 🟢 新客户端连接: *************:54322
2024-01-10 10:17:00.123 ℹ️ [系统] 🔴 TCP 服务器已停止监听 0.0.0.0:8080
```

## 🔧 技术实现

### 后端实现
- 在TCP连接建立时自动发送连接状态消息
- 在TCP连接断开时自动发送断开状态消息
- 包含完整的IP地址和端口信息

### 前端实现
- 自动接收并解析连接状态消息
- 在数据日志中以特殊格式显示
- 支持时间戳显示设置

## 💡 使用技巧

### 调试网络连接
1. **观察连接状态**: 通过状态消息快速了解连接是否成功建立
2. **监控连接稳定性**: 观察是否有频繁的连接断开和重连
3. **识别客户端**: 在服务器模式下，可以清楚看到哪些客户端连接了进来

### 故障排除
1. **连接失败**: 如果没有看到绿色的连接成功消息，检查网络配置
2. **频繁断开**: 如果看到频繁的红色断开消息，可能存在网络不稳定问题
3. **端口冲突**: 服务器启动失败时会显示具体的错误信息

## 🎨 自定义设置

### 时间戳显示
- **启用时间戳**: 显示详细的连接时间信息
- **禁用时间戳**: 只显示连接状态，界面更简洁

### 自动换行
- **启用自动换行**: 每个状态消息独占一行
- **禁用自动换行**: 状态消息连续显示

## 📈 优势

1. **实时监控**: 立即了解连接状态变化
2. **详细信息**: 显示完整的IP和端口信息
3. **易于识别**: 使用图标和颜色区分不同状态
4. **调试友好**: 帮助快速定位网络连接问题
5. **历史记录**: 所有连接状态都保存在数据日志中
