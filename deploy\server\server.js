import express from 'express';
import { WebSocketServer } from 'ws';
import http from 'http';
import cors from 'cors';
import { randomUUID } from 'crypto';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import ConnectionManager from './connectionManager.js';
import TCPServer from './tcpServer.js';
import TCPClient from './tcpClient.js';

// 获取当前文件目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 读取配置文件
let config;
try {
  const configPath = join(__dirname, '../config.json');
  config = JSON.parse(readFileSync(configPath, 'utf-8'));
} catch (error) {
  console.warn('配置文件读取失败，使用默认配置:', error.message);
  config = {
    server: { host: '0.0.0.0', port: 5000 },
    frontend: { host: '0.0.0.0', port: 5000 }
  };
}

const app = express();
const server = http.createServer(app);
const wss = new WebSocketServer({ server });

// 中间件
app.use(cors({
    origin: "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["*"]
}));

app.use(express.json());

// 全局实例
const connectionManager = new ConnectionManager();
const tcpServer = new TCPServer(connectionManager);
const tcpClient = new TCPClient(connectionManager);

// WebSocket 连接处理
wss.on('connection', (ws) => {
    const clientId = randomUUID();
    console.log(`新的 WebSocket 连接: ${clientId}`);

    connectionManager.addConnection(clientId, ws);

    // 发送连接确认
    ws.send(JSON.stringify({
        type: 'connected',
        clientId: clientId
    }));

    // 处理消息
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message.toString());
            console.log('收到 WebSocket 消息:', data);
            // 这里可以处理来自前端的特殊消息
        } catch (error) {
            console.error('解析 WebSocket 消息失败:', error);
        }
    });

    // 处理连接关闭
    ws.on('close', () => {
        console.log(`WebSocket 连接关闭: ${clientId}`);
        connectionManager.removeConnection(clientId);
    });

    // 处理错误
    ws.on('error', (error) => {
        console.error(`WebSocket 错误 ${clientId}:`, error);
        connectionManager.removeConnection(clientId);
    });
});

// API 路由

/**
 * 连接端点
 */
app.post('/api/connect', async (req, res) => {
    try {
        const { protocol, host, port, receiveSettings } = req.body;

        console.log('连接请求:', { protocol, host, port });

        if (protocol === 'tcp-server') {
            // TCP 服务器模式
            if (connectionManager.isServerRunning) {
                return res.json({ success: false, error: '服务器已在运行' });
            }

            try {
                await tcpServer.start(host, parseInt(port));
                res.json({ success: true });
            } catch (error) {
                console.error('启动 TCP 服务器失败:', error);
                res.json({ success: false, error: error.message });
            }
        } else if (protocol === 'tcp-client') {
            // TCP 客户端模式
            if (connectionManager.clientSocket) {
                return res.json({ success: false, error: '客户端已连接' });
            }

            try {
                await tcpClient.connect(host, parseInt(port));
                res.json({ success: true });
            } catch (error) {
                console.error('TCP 客户端连接失败:', error);
                res.json({ success: false, error: error.message });
            }
        } else {
            res.json({ success: false, error: '不支持的协议类型' });
        }
    } catch (error) {
        console.error('连接处理错误:', error);
        res.json({ success: false, error: error.message });
    }
});

/**
 * 断开连接端点
 */
app.post('/api/disconnect', async (req, res) => {
    try {
        await connectionManager.stopServer();
        await connectionManager.stopClient();
        res.json({ success: true });
    } catch (error) {
        console.error('断开连接错误:', error);
        res.json({ success: false, error: error.message });
    }
});

/**
 * 发送数据端点
 */
app.post('/api/send', async (req, res) => {
    try {
        const { content, format, appendNewline } = req.body;

        if (!content) {
            return res.json({ success: false, error: '发送内容不能为空' });
        }

        let dataToSend;

        // 根据格式处理数据
        if (format === 'hex') {
            // HEX 格式：将十六进制字符串转换为二进制数据
            try {
                const hexString = content.replace(/\s+/g, ''); // 移除空格
                if (!/^[0-9A-Fa-f]*$/.test(hexString)) {
                    return res.json({ success: false, error: 'HEX 格式无效，只能包含 0-9 和 A-F 字符' });
                }
                if (hexString.length % 2 !== 0) {
                    return res.json({ success: false, error: 'HEX 格式无效，字符数必须是偶数' });
                }

                const bytes = [];
                for (let i = 0; i < hexString.length; i += 2) {
                    bytes.push(parseInt(hexString.substr(i, 2), 16));
                }
                dataToSend = Buffer.from(bytes);
            } catch (error) {
                return res.json({ success: false, error: 'HEX 格式转换失败' });
            }
        } else {
            // ASCII 格式：直接使用字符串
            dataToSend = content;

            // 如果需要添加换行符
            if (appendNewline) {
                dataToSend += '\n';
            }

            dataToSend = Buffer.from(dataToSend, 'utf-8');
        }

        let success = false;

        if (connectionManager.isServerRunning) {
            // 服务器模式：向所有连接的客户端发送数据
            success = tcpServer.sendToAllClients(dataToSend);
        } else if (connectionManager.clientSocket) {
            // 客户端模式：向服务器发送数据
            success = tcpClient.sendToServer(dataToSend);
        }

        if (success) {
            res.json({ success: true });
        } else {
            res.json({ success: false, error: '没有活动连接或发送失败' });
        }
    } catch (error) {
        console.error('发送数据错误:', error);
        res.json({ success: false, error: error.message });
    }
});

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        serverRunning: connectionManager.isServerRunning,
        clientConnected: !!connectionManager.clientSocket,
        activeConnections: connectionManager.activeConnections.size
    });
});

// 添加静态文件服务（用于生产环境）
app.use(express.static(join(__dirname, '../dist')));

// 所有其他路由返回 index.html（用于 SPA）
app.get('*', (req, res) => {
    res.sendFile(join(__dirname, '../dist/index.html'));
});

// 启动服务器
const HOST = process.env.HOST || config.server.host;
const PORT = process.env.PORT || config.server.port;

server.listen(PORT, HOST, () => {
    console.log(`🚀 网络调试助手服务器已启动`);
    console.log(`📡 HTTP 服务器: http://${HOST}:${PORT}`);
    console.log(`🔌 WebSocket 服务器: ws://${HOST}:${PORT}/ws`);
    console.log(`📁 静态文件目录: ${join(__dirname, '../dist')}`);
    console.log(`⚙️  配置文件: ${join(__dirname, '../config.json')}`);
});

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('正在关闭服务器...');
    await connectionManager.stopServer();
    await connectionManager.stopClient();
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', async () => {
    console.log('正在关闭服务器...');
    await connectionManager.stopServer();
    await connectionManager.stopClient();
    server.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
