{"name": "network-debug-helper", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "build:prod": "npm run build && npm run package", "package": "node scripts/package.js", "test:deployment": "node scripts/test-deployment.js", "start": "node server/server.js", "pm2:start": "pm2 start ecosystem.config.cjs", "pm2:stop": "pm2 stop network-debug-helper", "pm2:restart": "pm2 restart network-debug-helper", "pm2:logs": "pm2 logs network-debug-helper"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "cors": "^2.8.5", "element-plus": "^2.9.3", "express": "^4.18.2", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vue": "^3.5.13", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "archiver": "^7.0.1", "fs-extra": "^11.2.0", "pm2": "^5.3.1", "typescript": "^5.7.3", "vite": "^6.0.7", "vue-tsc": "^2.2.0"}}