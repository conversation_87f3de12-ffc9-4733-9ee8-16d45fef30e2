{"name": "network-debug-helper", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/server.js", "server:prod": "cross-env NODE_ENV=production node server/server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "build:full": "npm run build && npm run server:prod", "start": "concurrently \"npm run server:prod\" \"npm run preview -- --port 5000 --host\"", "deploy": "npm run build && npm run start", "check": "node check-deployment.js", "package:centos": "node scripts/package-centos.js", "package:centos:linux": "chmod +x scripts/package-for-centos.sh && scripts/package-for-centos.sh", "setup:ip": "node scripts/setup-ip.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "cors": "^2.8.5", "element-plus": "^2.9.3", "express": "^4.18.2", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vue": "^3.5.13", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "archiver": "^7.0.1", "cross-env": "^7.0.3", "terser": "^5.39.2", "typescript": "^5.7.3", "vite": "^6.0.7", "vue-tsc": "^2.2.0"}}