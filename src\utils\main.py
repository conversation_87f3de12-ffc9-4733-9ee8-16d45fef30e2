from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio
import socket
import json
import datetime
from typing import Optional, Dict, Any, Set
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 数据模型
class ReceiveSettings(BaseModel):
    format: str
    showTimestamp: bool
    autoWrap: bool
    saveToFile: bool


class ConnectionConfig(BaseModel):
    protocol: str
    host: str
    port: str
    receiveSettings: ReceiveSettings


# 全局变量
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.server_socket: Optional[socket.socket] = None
        self.client_sockets: Set[socket.socket] = set()
        self.server_task: Optional[asyncio.Task] = None
        self.is_server_running = False
        self.current_websocket: Optional[WebSocket] = None  # 添加当前 WebSocket 引用
        self.client_socket: Optional[socket.socket] = None  # 添加 TCP Client socket
        self.client_task: Optional[asyncio.Task] = None  # 添加 TCP Client 任务

    async def stop_client(self):
        if self.client_task:
            self.client_task.cancel()
            try:
                await self.client_task
            except asyncio.CancelledError:
                pass
            self.client_task = None

        if self.client_socket:
            self.client_socket.close()
            self.client_socket = None

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        client_id = str(id(websocket))
        self.active_connections[client_id] = websocket
        self.current_websocket = websocket  # 保存当前 WebSocket
        return client_id

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            if self.current_websocket and str(id(self.current_websocket)) == client_id:
                self.current_websocket = None

    async def stop_server(self):
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
            self.server_task = None

        if self.server_socket:
            self.server_socket.close()
            self.server_socket = None

        for client_socket in self.client_sockets.copy():  # 使用 copy 避免在迭代时修改
            try:
                client_socket.close()
            except:
                pass
        self.client_sockets.clear()
        self.is_server_running = False


manager = ConnectionManager()


# WebSocket 连接处理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    client_id = await manager.connect(websocket)
    try:
        while True:
            try:
                # 保持连接活跃
                await websocket.receive_text()
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                break
    finally:
        manager.disconnect(client_id)


# TCP 服务器处理
async def run_tcp_server(host: str, port: int, client_id: str):
    try:
        manager.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        manager.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        manager.server_socket.bind((host, port))
        manager.server_socket.listen(5)
        manager.server_socket.setblocking(False)
        manager.is_server_running = True

        logger.info(f"TCP Server started on {host}:{port}")

        while manager.is_server_running:
            try:
                client_socket, addr = await asyncio.get_event_loop().sock_accept(manager.server_socket)
                logger.info(f"New client connected from {addr}")
                manager.client_sockets.add(client_socket)
                asyncio.create_task(handle_client_connection(client_socket, client_id, addr))
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error accepting connection: {e}")
                await asyncio.sleep(0.1)

    except Exception as e:
        logger.error(f"Server error: {e}")
        websocket = manager.get_websocket(client_id)
        if websocket:
            await websocket.send_json({
                "type": "error",
                "message": f"Server error: {str(e)}"
            })
    finally:
        await manager.stop_server()

def format_timestamp():
    now = datetime.datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 只保留3位毫秒


async def handle_client_connection(client_socket: socket.socket, client_id: str, addr: tuple):
    try:
        client_socket.setblocking(False)
        while True:
            try:
                data = await asyncio.get_event_loop().sock_recv(client_socket, 1024)
                if not data:
                    break

                if manager.current_websocket:
                    try:
                        if manager.current_websocket.client_state.value != 1:
                            break

                        await manager.current_websocket.send_json({
                            "type": "data",
                            "direction": "receive",
                            "data": data.decode('utf-8', errors='ignore'),
                            "timestamp": format_timestamp(),  # 使用新的时间格式
                            "client": f"{addr[0]}:{addr[1]}"
                        })
                    except RuntimeError as e:
                        logger.error(f"WebSocket send error: {e}")
                        break
                    except Exception as e:
                        logger.error(f"Unexpected WebSocket error: {e}")
                        break
            except ConnectionError:
                break
            except Exception as e:
                logger.error(f"Error receiving data: {e}")
                await asyncio.sleep(0.1)
    finally:
        try:
            client_socket.close()
            manager.client_sockets.remove(client_socket)
            logger.info(f"Client disconnected: {addr}")
        except:
            pass


# TCP Client 处理函数
async def run_tcp_client(host: str, port: int, client_id: str):
    try:
        manager.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        await asyncio.get_event_loop().sock_connect(manager.client_socket, (host, port))
        manager.client_socket.setblocking(False)

        logger.info(f"TCP Client connected to {host}:{port}")

        while True:
            try:
                data = await asyncio.get_event_loop().sock_recv(manager.client_socket, 1024)
                if not data:
                    break

                if manager.current_websocket:
                    try:
                        if manager.current_websocket.client_state.value != 1:
                            break

                        await manager.current_websocket.send_json({
                            "type": "data",
                            "direction": "receive",
                            "data": data.decode('utf-8', errors='ignore'),
                            "timestamp": format_timestamp(),
                            "client": f"{host}:{port}"
                        })
                    except RuntimeError as e:
                        logger.error(f"WebSocket send error: {e}")
                        break
                    except Exception as e:
                        logger.error(f"Unexpected WebSocket error: {e}")
                        break
            except ConnectionError:
                break
            except Exception as e:
                logger.error(f"Error receiving data: {e}")
                await asyncio.sleep(0.1)

    except Exception as e:
        logger.error(f"Client error: {e}")
        if manager.current_websocket:
            await manager.current_websocket.send_json({
                "type": "error",
                "message": f"Client error: {str(e)}"
            })
    finally:
        if manager.client_socket:
            manager.client_socket.close()
            manager.client_socket = None
# 连接端点
@app.post("/api/connect")
async def connect(config: ConnectionConfig):
    try:
        if config.protocol == "tcp-server":
            if manager.is_server_running:
                return {"success": False, "error": "Server is already running"}

            manager.server_task = asyncio.create_task(run_tcp_server(
                config.host,
                int(config.port),
                next(iter(manager.active_connections.keys()))
            ))
        else:  # TCP Client 模式
            if manager.client_socket:
                return {"success": False, "error": "Client is already connected"}

            manager.client_task = asyncio.create_task(run_tcp_client(
                config.host,
                int(config.port),
                next(iter(manager.active_connections.keys()))
            ))

        return {"success": True}
    except Exception as e:
        logger.error(f"Connection error: {e}")
        return {"success": False, "error": str(e)}


# 断开连接端点
@app.post("/api/disconnect")
async def disconnect():
    try:
        await manager.stop_server()  # 停止服务器
        await manager.stop_client()  # 停止客户端
        return {"success": True}
    except Exception as e:
        logger.error(f"Disconnect error: {e}")
        return {"success": False, "error": str(e)}



# 发送数据端点
@app.post("/api/send")
async def send_data(data: Dict[str, Any]):
    try:
        content = data['content'].encode()
        if manager.is_server_running:
            # 服务器模式：向所有连接的客户端发送数据
            for client_socket in manager.client_sockets:
                try:
                    await asyncio.get_event_loop().sock_sendall(client_socket, content)
                except:
                    continue
            return {"success": True}
        elif manager.client_socket:
            # 客户端模式：向服务器发送数据
            await asyncio.get_event_loop().sock_sendall(manager.client_socket, content)
            return {"success": True}
        return {"success": False, "error": "No active connection"}
    except Exception as e:
        logger.error(f"Send error: {e}")
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=3000)