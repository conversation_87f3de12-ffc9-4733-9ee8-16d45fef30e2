#!/bin/bash

# CentOS 自动安装脚本
# 自动安装 Node.js 和相关依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🚀 CentOS 环境自动安装脚本"
echo "此脚本将安装 Node.js 和相关依赖"
echo ""

# 检查是否为 root 用户
if [[ $EUID -ne 0 ]]; then
   log_error "此脚本需要 root 权限运行"
   echo "请使用: sudo $0"
   exit 1
fi

# 检测 CentOS 版本
if [ -f /etc/centos-release ]; then
    CENTOS_VERSION=$(cat /etc/centos-release | grep -oE '[0-9]+' | head -1)
    log_info "检测到 CentOS $CENTOS_VERSION"
elif [ -f /etc/redhat-release ]; then
    CENTOS_VERSION=$(cat /etc/redhat-release | grep -oE '[0-9]+' | head -1)
    log_info "检测到 RedHat $CENTOS_VERSION"
else
    log_warn "未检测到 CentOS/RedHat，假设为兼容系统"
    CENTOS_VERSION=8
fi

# 更新系统
log_info "更新系统包..."
if [ "$CENTOS_VERSION" -ge 8 ]; then
    dnf update -y
    dnf install -y curl wget tar gzip
else
    yum update -y
    yum install -y curl wget tar gzip
fi

# 安装 Node.js
log_info "安装 Node.js 18.x..."
if ! command -v node &> /dev/null; then
    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    
    # 安装 Node.js
    if [ "$CENTOS_VERSION" -ge 8 ]; then
        dnf install -y nodejs
    else
        yum install -y nodejs
    fi
    
    log_success "Node.js 安装完成: $(node -v)"
    log_success "npm 版本: $(npm -v)"
else
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ $NODE_VERSION -ge 16 ]; then
        log_success "Node.js 已安装: $(node -v)"
    else
        log_warn "Node.js 版本过低，正在升级..."
        curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
        if [ "$CENTOS_VERSION" -ge 8 ]; then
            dnf install -y nodejs
        else
            yum install -y nodejs
        fi
        log_success "Node.js 升级完成: $(node -v)"
    fi
fi

# 安装 PM2 (可选)
log_info "安装 PM2 进程管理器..."
npm install -g pm2
log_success "PM2 安装完成: $(pm2 -v)"

# 创建应用用户
log_info "创建应用用户..."
if ! id "nodejs" &>/dev/null; then
    useradd -r -s /bin/false nodejs
    log_success "用户 nodejs 创建完成"
else
    log_info "用户 nodejs 已存在"
fi

# 创建应用目录
log_info "创建应用目录..."
mkdir -p /opt/network-debug-helper
chown nodejs:nodejs /opt/network-debug-helper
log_success "应用目录创建完成: /opt/network-debug-helper"

# 配置防火墙
log_info "配置防火墙..."
if systemctl is-active --quiet firewalld; then
    firewall-cmd --permanent --add-port=3000/tcp
    firewall-cmd --permanent --add-port=5000/tcp
    firewall-cmd --reload
    log_success "防火墙端口已开放: 3000, 5000"
else
    log_warn "firewalld 未运行，请手动配置防火墙"
fi

# 安装 Nginx (可选)
read -p "是否安装 Nginx 作为反向代理? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "安装 Nginx..."
    if [ "$CENTOS_VERSION" -ge 8 ]; then
        dnf install -y nginx
    else
        yum install -y nginx
    fi
    
    systemctl enable nginx
    systemctl start nginx
    
    # 开放 HTTP 端口
    if systemctl is-active --quiet firewalld; then
        firewall-cmd --permanent --add-service=http
        firewall-cmd --reload
    fi
    
    log_success "Nginx 安装完成"
fi

# 配置 SELinux (如果启用)
if command -v getenforce &> /dev/null && [ "$(getenforce)" = "Enforcing" ]; then
    log_info "配置 SELinux..."
    setsebool -P httpd_can_network_connect 1
    log_success "SELinux 配置完成"
fi

# 创建日志目录
log_info "创建日志目录..."
mkdir -p /var/log/network-debug-helper
chown nodejs:nodejs /var/log/network-debug-helper
log_success "日志目录创建完成"

# 安装完成
echo ""
log_success "🎉 CentOS 环境安装完成！"
echo ""
echo "📋 安装摘要:"
echo "─────────────────────────────────────"
echo "✅ Node.js: $(node -v)"
echo "✅ npm: $(npm -v)"
echo "✅ PM2: $(pm2 -v)"
echo "✅ 应用用户: nodejs"
echo "✅ 应用目录: /opt/network-debug-helper"
echo "✅ 日志目录: /var/log/network-debug-helper"
echo "✅ 防火墙端口: 3000, 5000"
if command -v nginx &> /dev/null; then
    echo "✅ Nginx: $(nginx -v 2>&1 | cut -d' ' -f3)"
fi
echo ""
echo "🚀 下一步:"
echo "1. 上传应用部署包"
echo "2. 解压并安装应用"
echo "3. 启动服务"
echo ""
echo "📖 详细部署指南请参考 CENTOS_DEPLOY.md"
