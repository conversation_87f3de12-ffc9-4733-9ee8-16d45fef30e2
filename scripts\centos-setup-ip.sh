#!/bin/bash

# CentOS IP 配置脚本
# 简化版本，适用于服务器环境

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 获取本机IP地址
get_local_ips() {
    ip addr show | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | cut -d'/' -f1
}

# 验证IP地址
validate_ip() {
    local ip=$1
    if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        return 0
    elif [[ $ip == "localhost" ]] || [[ $ip == "0.0.0.0" ]]; then
        return 0
    else
        return 1
    fi
}

# 验证端口
validate_port() {
    local port=$1
    if [[ $port =~ ^[0-9]+$ ]] && [ $port -gt 0 ] && [ $port -le 65535 ]; then
        return 0
    else
        return 1
    fi
}

# 创建.env文件
create_env_file() {
    local server_host=$1
    local server_port=$2
    local api_host=$3
    local frontend_port=$4
    local environment=$5

    cat > .env << EOF
# 网络调试助手配置
# 由 centos-setup-ip.sh 脚本生成于 $(date)

# ================================
# 后端服务器配置
# ================================
SERVER_HOST=${server_host}
SERVER_PORT=${server_port}

# 生产环境后端配置
PROD_SERVER_HOST=${server_host}
PROD_SERVER_PORT=${server_port}

# ================================
# 前端配置
# ================================
CLIENT_DEV_PORT=5174
CLIENT_PROD_PORT=${frontend_port}
CLIENT_HOST=true

# ================================
# API 连接配置
# ================================
API_BASE_URL=http://${api_host}:${server_port}
WS_URL=ws://${api_host}:${server_port}/ws

# 生产环境 API 配置
PROD_API_BASE_URL=http://${api_host}:${server_port}
PROD_WS_URL=ws://${api_host}:${server_port}/ws

# ================================
# 运行环境
# ================================
NODE_ENV=${environment}
EOF

    log_success ".env 文件已创建"
}

# 显示配置预览
show_config_preview() {
    local server_host=$1
    local server_port=$2
    local api_host=$3
    local frontend_port=$4
    local environment=$5

    echo ""
    log_info "配置预览:"
    echo -e "${CYAN}──────────────────────────────────────────────────${NC}"
    echo -e "${CYAN}后端监听地址: ${server_host}:${server_port}${NC}"
    echo -e "${CYAN}前端访问地址: http://${api_host}:${frontend_port}${NC}"
    echo -e "${CYAN}API 连接地址: http://${api_host}:${server_port}${NC}"
    echo -e "${CYAN}WebSocket 地址: ws://${api_host}:${server_port}/ws${NC}"
    echo -e "${CYAN}运行环境: ${environment}${NC}"
    echo -e "${CYAN}──────────────────────────────────────────────────${NC}"
}

# 主配置流程
main() {
    log_info "网络调试助手 IP 配置向导 (CentOS 版本)"
    echo ""

    # 获取本机IP
    local_ips=$(get_local_ips)
    
    if [ -n "$local_ips" ]; then
        log_info "检测到的网络接口:"
        echo "$local_ips" | nl -w2 -s'. '
        echo ""
    fi

    # 选择配置模式
    log_info "请选择配置模式:"
    echo "  1. 本地开发 (localhost)"
    echo "  2. 局域网访问 (自动检测IP)"
    echo "  3. 自定义配置"
    echo ""

    read -p "请选择模式 (1-3): " mode

    case $mode in
        1)
            # 本地开发模式
            server_host="0.0.0.0"
            server_port="3000"
            api_host="localhost"
            frontend_port="5000"
            environment="development"
            ;;
        2)
            # 局域网访问模式
            if [ -z "$local_ips" ]; then
                log_error "未检测到可用的网络接口"
                exit 1
            fi

            # 自动选择第一个IP
            selected_ip=$(echo "$local_ips" | head -n1)
            log_success "自动选择IP: $selected_ip"

            server_host="0.0.0.0"
            server_port="3000"
            api_host="$selected_ip"
            frontend_port="5000"
            environment="production"
            ;;
        3)
            # 自定义配置模式
            log_info "自定义配置模式"
            
            # 后端监听地址
            while true; do
                read -p "后端监听地址 (0.0.0.0): " server_host
                server_host=${server_host:-"0.0.0.0"}
                if validate_ip "$server_host"; then
                    break
                else
                    log_error "无效的IP地址格式"
                fi
            done

            # 后端端口
            while true; do
                read -p "后端端口 (3000): " server_port
                server_port=${server_port:-"3000"}
                if validate_port "$server_port"; then
                    break
                else
                    log_error "无效的端口号 (1-65535)"
                fi
            done

            # 前端连接IP
            while true; do
                read -p "前端连接IP (localhost): " api_host
                api_host=${api_host:-"localhost"}
                if validate_ip "$api_host"; then
                    break
                else
                    log_error "无效的IP地址格式"
                fi
            done

            # 前端端口
            while true; do
                read -p "前端端口 (5000): " frontend_port
                frontend_port=${frontend_port:-"5000"}
                if validate_port "$frontend_port"; then
                    break
                else
                    log_error "无效的端口号 (1-65535)"
                fi
            done

            # 运行环境
            read -p "运行环境 (development/production) [production]: " environment
            environment=${environment:-"production"}
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac

    # 显示配置预览
    show_config_preview "$server_host" "$server_port" "$api_host" "$frontend_port" "$environment"

    # 确认配置
    echo ""
    read -p "是否应用此配置? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_warn "配置已取消"
        exit 0
    fi

    # 创建配置文件
    create_env_file "$server_host" "$server_port" "$api_host" "$frontend_port" "$environment"

    # 显示后续步骤
    echo ""
    log_success "配置完成！"
    echo ""
    log_info "后续步骤:"
    echo -e "${CYAN}1. 重启服务以应用新配置:${NC}"
    echo -e "${CYAN}   npm start${NC}"
    echo ""
    echo -e "${CYAN}2. 访问地址:${NC}"
    echo -e "${CYAN}   前端: http://${api_host}:${frontend_port}${NC}"
    echo -e "${CYAN}   API:  http://${api_host}:${server_port}${NC}"
    echo ""
    echo -e "${CYAN}3. 验证配置:${NC}"
    echo -e "${CYAN}   curl http://${api_host}:${server_port}/api/health${NC}"
}

# 运行主程序
main "$@"
