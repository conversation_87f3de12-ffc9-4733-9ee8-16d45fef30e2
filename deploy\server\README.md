# 网络调试助手 - Node.js 后端

这是网络调试助手的 Node.js 后端实现，替代了原来的 Python FastAPI 后端。

## 功能特性

- **TCP 服务器模式**: 监听指定端口，接受客户端连接
- **TCP 客户端模式**: 连接到远程 TCP 服务器
- **WebSocket 实时通信**: 与前端进行实时数据交换
- **数据格式支持**: ASCII 和 HEX 格式
- **错误处理**: 完善的错误处理和状态管理

## 技术栈

- **Express.js**: Web 框架
- **ws**: WebSocket 库
- **net**: Node.js 内置 TCP 模块
- **cors**: 跨域处理

## API 端点

### 1. 连接 `/api/connect`
- **方法**: POST
- **功能**: 建立 TCP 连接（服务器或客户端模式）
- **请求体**:
```json
{
  "protocol": "tcp-server|tcp-client",
  "host": "*************",
  "port": "8071",
  "receiveSettings": {
    "format": "ascii|hex",
    "showTimestamp": true,
    "autoWrap": true,
    "saveToFile": false
  }
}
```

### 2. 断开连接 `/api/disconnect`
- **方法**: POST
- **功能**: 断开所有 TCP 连接

### 3. 发送数据 `/api/send`
- **方法**: POST
- **功能**: 发送数据到 TCP 连接
- **请求体**:
```json
{
  "content": "要发送的数据",
  "format": "ascii|hex",
  "appendNewline": false
}
```

### 4. 健康检查 `/api/health`
- **方法**: GET
- **功能**: 检查服务器状态

## WebSocket 消息格式

### 接收数据消息
```json
{
  "type": "data",
  "direction": "receive",
  "data": "接收到的数据",
  "timestamp": "2024-01-10 10:15:23.456",
  "client": "*************:8071"
}
```

### 错误消息
```json
{
  "type": "error",
  "message": "错误描述"
}
```

## 启动方式

### 开发模式
```bash
# 只启动后端
npm run server

# 同时启动前端和后端
npm run dev:full
```

### 生产模式
```bash
node server/server.js
```

## 端口配置

- 默认端口: 3000
- 可通过环境变量 `PORT` 修改

## 文件结构

```
server/
├── server.js              # 主服务器文件
├── connectionManager.js   # 连接管理器
├── tcpServer.js           # TCP 服务器处理
├── tcpClient.js           # TCP 客户端处理
└── README.md              # 说明文档
```

## 与 Python 版本的差异

1. **性能**: Node.js 版本在处理并发连接时性能更好
2. **内存使用**: 更低的内存占用
3. **部署**: 更简单的部署流程
4. **维护**: 统一的 JavaScript 技术栈，便于维护

## 注意事项

- 确保端口 3000 未被占用
- TCP 服务器模式需要确保监听端口可用
- TCP 客户端模式需要确保目标服务器可达
