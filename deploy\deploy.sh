#!/bin/bash

# 网络调试助手部署脚本
# 适用于 CentOS/Ubuntu/Debian 等 Linux 系统

set -e

echo "🚀 开始部署网络调试助手..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "检测到 root 用户，建议使用普通用户运行"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测系统类型
detect_system() {
    if [[ -f /etc/redhat-release ]]; then
        SYSTEM="centos"
        PACKAGE_MANAGER="yum"
    elif [[ -f /etc/debian_version ]]; then
        SYSTEM="debian"
        PACKAGE_MANAGER="apt"
    else
        print_error "不支持的系统类型"
        exit 1
    fi
    print_message "检测到系统类型: $SYSTEM"
}

# 安装 Node.js
install_nodejs() {
    print_step "检查 Node.js 安装状态..."

    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_message "Node.js 已安装: $NODE_VERSION"

        # 检查版本是否满足要求 (>= 16)
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [[ $MAJOR_VERSION -lt 16 ]]; then
            print_warning "Node.js 版本过低，需要 >= 16.0.0"
            install_node=true
        else
            install_node=false
        fi
    else
        print_message "Node.js 未安装，开始安装..."
        install_node=true
    fi

    if [[ $install_node == true ]]; then
        print_step "安装 Node.js..."

        # 使用 NodeSource 仓库安装最新 LTS 版本
        if [[ $SYSTEM == "centos" ]]; then
            curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            sudo $PACKAGE_MANAGER install -y nodejs
        else
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo $PACKAGE_MANAGER update
            sudo $PACKAGE_MANAGER install -y nodejs
        fi

        print_message "Node.js 安装完成: $(node --version)"
    fi
}

# 安装 PM2
install_pm2() {
    print_step "检查 PM2 安装状态..."

    if command -v pm2 &> /dev/null; then
        print_message "PM2 已安装: $(pm2 --version)"
    else
        print_step "安装 PM2..."
        npm install -g pm2
        print_message "PM2 安装完成"
    fi
}

# 安装依赖
install_dependencies() {
    print_step "安装项目依赖..."

    if [[ ! -f package.json ]]; then
        print_error "package.json 文件不存在"
        exit 1
    fi

    npm install --production
    print_message "依赖安装完成"
}

# 创建日志目录
create_log_directory() {
    print_step "创建日志目录..."
    mkdir -p logs
    print_message "日志目录创建完成"
}

# 配置服务器IP
configure_server_ip() {
    print_step "配置服务器IP地址..."

    if [[ -f config.json ]]; then
        print_message "发现配置文件 config.json"

        # 检查是否需要自定义IP
        AUTO_DETECT=$(grep -o '"auto_detect_ip"[[:space:]]*:[[:space:]]*[^,}]*' config.json | grep -o '[^:]*$' | tr -d ' "')
        CUSTOM_IP=$(grep -o '"custom_ip"[[:space:]]*:[[:space:]]*"[^"]*"' config.json | grep -o '"[^"]*"$' | tr -d '"')

        if [[ "$AUTO_DETECT" == "false" && -n "$CUSTOM_IP" ]]; then
            print_message "使用自定义IP地址: $CUSTOM_IP"
            SERVER_IP="$CUSTOM_IP"
        else
            print_message "自动检测服务器IP地址"
            SERVER_IP=$(hostname -I | awk '{print $1}')
        fi
    else
        print_message "未找到配置文件，自动检测IP地址"
        SERVER_IP=$(hostname -I | awk '{print $1}')
    fi

    print_message "服务器IP地址: $SERVER_IP"

    # 询问用户是否要修改IP地址
    echo
    print_message "当前检测到的服务器IP: $SERVER_IP"
    read -p "是否要使用自定义IP地址? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "请输入自定义IP地址: " CUSTOM_SERVER_IP
        if [[ -n "$CUSTOM_SERVER_IP" ]]; then
            SERVER_IP="$CUSTOM_SERVER_IP"
            print_message "使用自定义IP地址: $SERVER_IP"
        fi
    fi
}

# 检查端口占用
check_ports() {
    print_step "检查端口占用情况..."

    PORTS=(3000 5000)
    for port in "${PORTS[@]}"; do
        if netstat -tuln | grep ":$port " > /dev/null; then
            print_warning "端口 $port 已被占用"
            print_message "正在尝试停止占用端口的进程..."

            # 尝试用 PM2 停止
            pm2 stop network-debug-helper 2>/dev/null || true
            pm2 stop network-debug-helper-frontend 2>/dev/null || true

            sleep 2

            if netstat -tuln | grep ":$port " > /dev/null; then
                print_error "端口 $port 仍被占用，请手动处理"
                print_message "可以使用以下命令查看占用进程: lsof -i :$port"
                exit 1
            fi
        else
            print_message "端口 $port 可用"
        fi
    done
}

# 启动服务
start_services() {
    print_step "启动服务..."

    # 停止可能存在的旧进程
    pm2 stop network-debug-helper 2>/dev/null || true
    pm2 stop network-debug-helper-frontend 2>/dev/null || true
    pm2 delete network-debug-helper 2>/dev/null || true
    pm2 delete network-debug-helper-frontend 2>/dev/null || true

    # 设置环境变量
    export HOST="$SERVER_IP"
    export PORT=3000
    export FRONTEND_PORT=5000

    print_message "使用IP地址: $SERVER_IP"
    print_message "后端端口: $PORT"
    print_message "前端端口: $FRONTEND_PORT"

    # 启动新服务
    pm2 start ecosystem.config.cjs

    # 保存 PM2 配置
    pm2 save

    # 设置开机自启
    pm2 startup

    print_message "服务启动完成"
}

# 显示服务状态
show_status() {
    print_step "服务状态:"
    pm2 status

    echo
    print_message "🎉 部署完成!"
    print_message "📋 服务信息:"
    print_message "  - 前端地址: http://${SERVER_IP}:5000"
    print_message "  - 后端地址: http://${SERVER_IP}:3000"
    print_message "  - 日志目录: $(pwd)/logs"
    echo
    print_message "🔧 常用命令:"
    print_message "  - 查看状态: pm2 status"
    print_message "  - 查看日志: pm2 logs"
    print_message "  - 重启服务: pm2 restart all"
    print_message "  - 停止服务: pm2 stop all"
    echo
    print_message "📊 实时日志:"
    print_message "  - 后端日志: pm2 logs network-debug-helper"
    print_message "  - 前端日志: pm2 logs network-debug-helper-frontend"
    print_message "  - 所有日志: pm2 logs"
}

# 主函数
main() {
    print_message "网络调试助手自动部署脚本"
    print_message "适用于 CentOS/Ubuntu/Debian 系统"
    echo

    check_root
    detect_system
    install_nodejs
    install_pm2
    install_dependencies
    create_log_directory
    configure_server_ip
    check_ports
    start_services
    show_status
}

# 执行主函数
main "$@"
