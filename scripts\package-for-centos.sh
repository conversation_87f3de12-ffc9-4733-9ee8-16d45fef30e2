#!/bin/bash

# 网络调试助手 CentOS 打包脚本
# 创建可在 CentOS 上部署的完整包

set -e

echo "🚀 开始打包网络调试助手 for CentOS..."

# 配置
PACKAGE_NAME="network-debug-helper"
VERSION=$(node -p "require('./package.json').version")
BUILD_DIR="build"
PACKAGE_DIR="${BUILD_DIR}/${PACKAGE_NAME}-${VERSION}"
ARCHIVE_NAME="${PACKAGE_NAME}-${VERSION}-centos.tar.gz"

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf ${BUILD_DIR}
mkdir -p ${PACKAGE_DIR}

# 安装依赖
echo "📦 安装依赖..."
npm ci --production=false

# 构建前端
echo "🔨 构建前端..."
npm run build

# 复制必要文件到打包目录
echo "📋 复制文件..."

# 复制构建后的前端文件
cp -r dist ${PACKAGE_DIR}/

# 复制后端源码
cp -r server ${PACKAGE_DIR}/

# 复制配置文件
cp package.json ${PACKAGE_DIR}/
cp package-lock.json ${PACKAGE_DIR}/

# 复制部署脚本
mkdir -p ${PACKAGE_DIR}/scripts
cp scripts/centos-*.sh ${PACKAGE_DIR}/scripts/ 2>/dev/null || true

# 创建生产环境的 package.json（只包含生产依赖）
echo "📝 创建生产环境 package.json..."
node -e "
const pkg = require('./package.json');
const prodPkg = {
  name: pkg.name,
  version: pkg.version,
  description: pkg.description,
  type: pkg.type,
  scripts: {
    'start': 'node server/server.js',
    'check': 'node check-deployment.js'
  },
  dependencies: {
    'express': pkg.dependencies.express,
    'ws': pkg.dependencies.ws,
    'cors': pkg.dependencies.cors
  }
};
require('fs').writeFileSync('${PACKAGE_DIR}/package.json', JSON.stringify(prodPkg, null, 2));
"

# 复制部署相关文件
cp README.md ${PACKAGE_DIR}/
cp DEPLOYMENT_GUIDE.md ${PACKAGE_DIR}/
cp check-deployment.js ${PACKAGE_DIR}/

# 创建 CentOS 部署脚本
cat > ${PACKAGE_DIR}/install.sh << 'EOF'
#!/bin/bash

# CentOS 安装脚本
set -e

echo "🚀 开始在 CentOS 上安装网络调试助手..."

# 检查是否为 root 用户
if [[ $EUID -eq 0 ]]; then
   echo "⚠️  建议不要使用 root 用户运行此脚本"
   read -p "是否继续? (y/N): " -n 1 -r
   echo
   if [[ ! $REPLY =~ ^[Yy]$ ]]; then
       exit 1
   fi
fi

# 检查 Node.js
echo "📋 检查 Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
    echo "安装命令："
    echo "curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -"
    echo "sudo yum install -y nodejs"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，需要 16+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 安装依赖
echo "📦 安装依赖..."
npm install --production

# 设置权限
chmod +x scripts/*.sh 2>/dev/null || true

# 创建服务目录
APP_DIR="/opt/network-debug-helper"
if [ ! -d "$APP_DIR" ]; then
    echo "📁 创建应用目录..."
    sudo mkdir -p $APP_DIR
    sudo chown $USER:$USER $APP_DIR
fi

echo "✅ 安装完成！"
echo ""
echo "🚀 启动应用："
echo "npm start"
echo ""
echo "🔍 检查状态："
echo "npm run check"
echo ""
echo "🌐 访问地址："
echo "http://localhost:5000"

EOF

# 创建 systemd 服务文件
cat > ${PACKAGE_DIR}/network-debug-helper.service << 'EOF'
[Unit]
Description=Network Debug Helper
After=network.target

[Service]
Type=simple
User=nodejs
WorkingDirectory=/opt/network-debug-helper
ExecStart=/usr/bin/node server/server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=HOST=0.0.0.0

# 日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=network-debug-helper

[Install]
WantedBy=multi-user.target
EOF

# 创建 Nginx 配置文件
cat > ${PACKAGE_DIR}/nginx.conf << 'EOF'
server {
    listen 5000;
    server_name localhost;
    
    # 前端静态文件
    location / {
        root /opt/network-debug-helper/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # WebSocket 代理
    location /ws {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 创建部署说明文件
cat > ${PACKAGE_DIR}/CENTOS_DEPLOY.md << 'EOF'
# CentOS 部署指南

## 系统要求
- CentOS 7/8/9
- Node.js 16+
- 至少 512MB 内存
- 端口 3000 和 5000 可用

## 快速部署

### 1. 解压文件
```bash
tar -xzf network-debug-helper-*-centos.tar.gz
cd network-debug-helper-*
```

### 2. 运行安装脚本
```bash
chmod +x install.sh
./install.sh
```

### 3. 启动应用
```bash
npm start
```

### 4. 验证部署
```bash
npm run check
```

## 生产环境部署

### 使用 systemd 服务

1. 复制文件到系统目录：
```bash
sudo cp -r . /opt/network-debug-helper
sudo chown -R nodejs:nodejs /opt/network-debug-helper
```

2. 安装 systemd 服务：
```bash
sudo cp network-debug-helper.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable network-debug-helper
sudo systemctl start network-debug-helper
```

3. 检查服务状态：
```bash
sudo systemctl status network-debug-helper
```

### 使用 Nginx 反向代理

1. 安装 Nginx：
```bash
sudo yum install -y nginx
```

2. 配置 Nginx：
```bash
sudo cp nginx.conf /etc/nginx/conf.d/network-debug-helper.conf
sudo nginx -t
sudo systemctl restart nginx
```

## 防火墙配置

```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

## 故障排除

### 查看日志
```bash
# 应用日志
sudo journalctl -u network-debug-helper -f

# Nginx 日志
sudo tail -f /var/log/nginx/error.log
```

### 常见问题
1. 端口被占用：修改配置文件中的端口
2. 权限问题：确保 nodejs 用户有正确权限
3. 防火墙阻止：检查防火墙配置
EOF

# 设置执行权限
chmod +x ${PACKAGE_DIR}/install.sh

# 创建压缩包
echo "📦 创建压缩包..."
cd ${BUILD_DIR}
tar -czf ${ARCHIVE_NAME} ${PACKAGE_NAME}-${VERSION}
cd ..

# 移动到根目录
mv ${BUILD_DIR}/${ARCHIVE_NAME} ./

echo "✅ 打包完成！"
echo ""
echo "📦 打包文件: ${ARCHIVE_NAME}"
echo "📁 大小: $(du -h ${ARCHIVE_NAME} | cut -f1)"
echo ""
echo "🚀 CentOS 部署步骤："
echo "1. 上传 ${ARCHIVE_NAME} 到 CentOS 服务器"
echo "2. tar -xzf ${ARCHIVE_NAME}"
echo "3. cd ${PACKAGE_NAME}-${VERSION}"
echo "4. chmod +x install.sh && ./install.sh"
echo "5. npm start"
echo ""
echo "🌐 访问地址: http://服务器IP:5000"
